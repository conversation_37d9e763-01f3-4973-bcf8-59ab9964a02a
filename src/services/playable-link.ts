import { and, eq } from 'drizzle-orm'

import * as schema from '../db/schema'

import type { DrizzleD1Database } from 'drizzle-orm/d1'

export class PlayableLinkService {
  static async upsertPlayableLink(
    db: DrizzleD1Database<typeof schema>,
    youtubeId: string,
    playableLink: string,
    type: 'video' | 'audio',
  ) {
    const data = {
      youtubeId,
      playableLink,
      type,
    }

    await db
      .insert(schema.playableLinks)
      .values(data)
      .onConflictDoUpdate({
        target: [schema.playableLinks.youtubeId, schema.playableLinks.type],
        set: { playableLink, updatedAt: new Date() },
      })
  }

  static async getPlayableLink(
    db: DrizzleD1Database<typeof schema>,
    youtubeId: string,
    type: 'video' | 'audio',
  ) {
    const link = await db
      .select({
        playableLink: schema.playableLinks.playableLink,
      })
      .from(schema.playableLinks)
      .where(
        and(eq(schema.playableLinks.youtubeId, youtubeId), eq(schema.playableLinks.type, type)),
      )
      .get()

    if (!link) {
      throw new Error(`Playable link not found for youtubeId: ${youtubeId} and type: ${type}`)
    }
    return link
  }

  static async getPlayableLinkWithUpdatedAt(
    db: DrizzleD1Database<typeof schema>,
    youtubeId: string,
    type: 'video' | 'audio',
  ) {
    const link = await db
      .select({
        playableLink: schema.playableLinks.playableLink,
        updatedAt: schema.playableLinks.updatedAt,
      })
      .from(schema.playableLinks)
      .where(
        and(eq(schema.playableLinks.youtubeId, youtubeId), eq(schema.playableLinks.type, type)),
      )
      .get()

    if (!link) {
      throw new Error(`Playable link not found for youtubeId: ${youtubeId} and type: ${type}`)
    }
    return { link, updatedAt: link.updatedAt }
  }
}
