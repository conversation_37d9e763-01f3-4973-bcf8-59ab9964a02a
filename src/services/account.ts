import { eq, gt, or } from 'drizzle-orm'

import * as schema from '../db/schema'
import { API_MIN_THRESHOLD } from '../utils'

import type { DrizzleD1Database } from 'drizzle-orm/d1'

class RecordNotFoundError extends Error {
  constructor(message = 'Record not found') {
    super(message)
    this.name = 'RecordNotFoundError'
  }
}

export class AccountService {
  static async getAvailableAccount(db: DrizzleD1Database<typeof schema>) {
    try {
      const account = await db
        .select({
          id: schema.rapidApiAccounts.id,
          apiKey: schema.rapidApiAccounts.apiKey,
          api1Remain: schema.rapidApiAccounts.api1Remain,
        })
        .from(schema.rapidApiAccounts)
        .where(
          or(
            gt(schema.rapidApiAccounts.api1Remain, API_MIN_THRESHOLD),
            gt(schema.rapidApiAccounts.api2Remain, API_MIN_THRESHOLD),
          ),
        )
        .get()

      if (!account) {
        throw new RecordNotFoundError(
          'No available RapidAPI account with api1Remain or api2Remain > 5.',
        )
      }
      return account
    } catch (error) {
      if (error instanceof RecordNotFoundError) {
        await db.update(schema.rapidApiAccounts).set({
          api1Remain: 300,
          api2Remain: 300,
        })

        const retryAccount = await db
          .select({
            id: schema.rapidApiAccounts.id,
            apiKey: schema.rapidApiAccounts.apiKey,
            api1Remain: schema.rapidApiAccounts.api1Remain,
          })
          .from(schema.rapidApiAccounts)
          .where(
            or(
              gt(schema.rapidApiAccounts.api1Remain, API_MIN_THRESHOLD),
              gt(schema.rapidApiAccounts.api2Remain, API_MIN_THRESHOLD),
            ),
          )
          .get()

        if (!retryAccount) {
          throw new RecordNotFoundError(
            'Failed to find an available account even after resetting limits.',
          )
        }
        return retryAccount
      }
      throw error
    }
  }

  static async getAvailableAudioAccount(db: DrizzleD1Database<typeof schema>) {
    try {
      const account = await db
        .select({
          id: schema.rapidApiAccounts.id,
          apiKey: schema.rapidApiAccounts.apiKey,
        })
        .from(schema.rapidApiAccounts)
        .where(gt(schema.rapidApiAccounts.api3Remain, API_MIN_THRESHOLD))
        .get()

      if (!account) {
        throw new RecordNotFoundError('No available RapidAPI account with api3Remain > 5.')
      }
      return account
    } catch (error) {
      if (error instanceof RecordNotFoundError) {
        await db.update(schema.rapidApiAccounts).set({
          api3Remain: 300,
        })

        const retryAccount = await db
          .select({
            id: schema.rapidApiAccounts.id,
            apiKey: schema.rapidApiAccounts.apiKey,
          })
          .from(schema.rapidApiAccounts)
          .where(gt(schema.rapidApiAccounts.api3Remain, API_MIN_THRESHOLD))
          .get()

        if (!retryAccount) {
          throw new RecordNotFoundError(
            'Failed to find an available audio account even after resetting api3Remain.',
          )
        }
        return retryAccount
      }
      throw error
    }
  }

  static async updateApiRemaining(
    db: DrizzleD1Database<typeof schema>,
    accountId: number,
    updates: Partial<
      Pick<schema.RapidApiAccount, 'api1Remain' | 'api2Remain' | 'api3Remain' | 'api4Remain'>
    >,
  ) {
    const validUpdates: { [key: string]: number | null } = {}
    for (const key in updates) {
      if (
        Object.prototype.hasOwnProperty.call(updates, key) &&
        (key === 'api1Remain' ||
          key === 'api2Remain' ||
          key === 'api3Remain' ||
          key === 'api4Remain')
      ) {
        validUpdates[key] = (updates as Record<string, number | null>)[key]
      }
    }

    if (Object.keys(validUpdates).length === 0) {
      return db
        .select()
        .from(schema.rapidApiAccounts)
        .where(eq(schema.rapidApiAccounts.id, accountId))
        .get()
    }

    await db
      .update(schema.rapidApiAccounts)
      .set({ ...validUpdates, updatedAt: new Date() })
      .where(eq(schema.rapidApiAccounts.id, accountId))
  }

  static async updateLastUsed(db: DrizzleD1Database<typeof schema>, accountId: number) {
    await db
      .update(schema.rapidApiAccounts)
      .set({
        lastUsed: new Date(),
        updatedAt: new Date(),
      })
      .where(eq(schema.rapidApiAccounts.id, accountId))
  }
}
