import { fetcher } from 'itty-fetcher'

import { formatAudioLink } from '../utils'

import type { Mp3, YoutubeVideo } from '../types'

export class ApiService {
  private static readonly API_HOSTS = {
    VIDEO_API_1: String.fromCharCode(
      ...[
        121, 116, 115, 116, 114, 101, 97, 109, 45, 100, 111, 119, 110, 108, 111, 97, 100, 45, 121,
        111, 117, 116, 117, 98, 101, 45, 118, 105, 100, 101, 111, 115, 46, 112, 46, 114, 97, 112,
        105, 100, 97, 112, 105, 46, 99, 111, 109,
      ],
    ),
    VIDEO_API_2: String.fromCharCode(
      ...[
        121, 116, 45, 97, 112, 105, 46, 112, 46, 114, 97, 112, 105, 100, 97, 112, 105, 46, 99, 111,
        109,
      ],
    ),
    AUDIO_API: String.fromCharCode(
      ...[
        121, 111, 117, 116, 117, 98, 101, 45, 109, 112, 51, 54, 46, 112, 46, 114, 97, 112, 105, 100,
        97, 112, 105, 46, 99, 111, 109,
      ],
    ),
  }

  static async fetchVideoData(videoId: string, apiKey: string, useApi1: boolean, cgeo: string) {
    const apiHost = useApi1 ? this.API_HOSTS.VIDEO_API_1 : this.API_HOSTS.VIDEO_API_2

    const response: Response = await fetcher({ parse: false }).get(`https://${apiHost}/dl`, {
      headers: {
        'x-rapidapi-host': apiHost,
        'x-rapidapi-key': apiKey,
      },
      query: { id: videoId, cgeo },
    })

    const apiRemain = Number(
      response.headers.get('x-ratelimit-requests-remaining') ??
        response.headers.get('x-ratelimit-request-remaining') ??
        -1,
    )
    const data = await response.json<YoutubeVideo>()

    return { data, apiRemain }
  }

  static async fetchAudioData(videoId: string, apiKey: string) {
    const response: Response = await fetcher({ parse: false }).get(
      `https://${this.API_HOSTS.AUDIO_API}/dl`,
      {
        headers: {
          'x-rapidapi-host': this.API_HOSTS.AUDIO_API,
          'x-rapidapi-key': apiKey,
        },
        query: { id: videoId },
      },
    )

    const apiRemain = Number(
      response.headers.get('x-ratelimit-requests-remaining') ??
        response.headers.get('x-ratelimit-request-remaining') ??
        -1,
    )
    const data = await response.json<Mp3>()
    data.link = formatAudioLink(data.link)

    return { data, apiRemain }
  }
}
