import { HTTPException } from 'hono/http-exception'
import { requestId } from 'hono/request-id'

import { Hono } from 'hono'

import { audioPlaybackHandler, relayHandler, videoPlaybackHandler } from './handlers'
import { spotifyToYtmusicHandler } from './handlers/audioplayback'
import { shortsStreamHandler } from './handlers/shorts-stream'
import { drizzle } from './middlewares'
import apiRouter from './routes/api'
import channelRouter from './routes/channel'
import detailRouter from './routes/detail'
import homeRouter from './routes/home'
import searchRouter from './routes/search'
import ytImgRouter from './routes/yt-img'
import { m3uTemplate, message, validateVideoId, webBaseUrl } from './utils'

const app = new Hono<AppEnv>()

app.route('/api/v1', apiRouter)

app.route('/', homeRouter)
app.route('/', searchRouter)
app.route('/', channelRouter)
app.route('/', detailRouter)

app.get('/shorts', requestId(), drizzle(), async (c) => {
  const v = c.req.query('v')
  if (!v || !validateVideoId(v)) {
    throw new HTTPException(400, { message: message.INVALID_VIDEO_ID })
  }

  const reqId = c.get('requestId')

  await c.env.YT_SHORTS.put(`${reqId}:stream1`, v, {
    expirationTtl: 60 * 60 * 24,
  })

  return c.newResponse(
    m3uTemplate(
      `${webBaseUrl}/shorts-stream/stream1?reqId=${reqId}`,
      `${webBaseUrl}/shorts-stream/stream2?reqId=${reqId}`,
    ),
    {
      headers: {
        'Content-Type': 'audio/x-mpegurl',
      },
    },
  )
})

app.get('/shorts-stream/:streamId', drizzle(), (c) => {
  const streamId = c.req.param('streamId')

  if (!streamId || !['stream1', 'stream2'].includes(streamId)) {
    throw new HTTPException(400, { message: 'Invalid stream ID' })
  }

  return shortsStreamHandler(c, streamId)
})

app.get('/videoplayback', drizzle(), videoPlaybackHandler)
app.get('/audioplayback', drizzle(), audioPlaybackHandler)
app.get('/spotify-to-ytmusic', drizzle(), spotifyToYtmusicHandler)

app.all('/relay', drizzle(), relayHandler)

app.route('/', ytImgRouter)

export default {
  fetch: app.fetch,
  scheduled: async () => {
    await fetch('https://youthful-karlee-phd051199-a05a3eca.koyeb.app/', {
      headers: {
        'User-Agent': 'Cloudflare Worker',
      },
    })
  },
}
