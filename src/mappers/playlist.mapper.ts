import { YTNodes } from 'youtubei.js/cf-worker'

import { parseCount, parseDuration, parseRelativeTimeToTimestamp } from '../utils'

/* eslint-disable @typescript-eslint/no-explicit-any */
type Playlist = {
  type: 'playlist'
  title: string
  playlistId: string
  playlistThumbnail: string
  author: string
  authorId: string
  authorUrl: string
  authorVerified: boolean
  videoCount: number
  videos: any[]
}

export function mapPlaylists(data: any, channel: any): { playlists: Playlist[] } {
  const contents = data.contents?.[0]?.items ?? []
  const author = channel.metadata.title || ''
  const authorId = channel.metadata.external_id || ''
  const authorUrl = `/channel/${authorId}`

  const playlists: Playlist[] = contents
    .filter((item: any) => item.type === 'LockupView')
    .map((item: any): Playlist => {
      const playlistId = item.content_id
      const title = item.metadata?.title?.text ?? ''
      const thumbnailUrl = item.content_image?.primary_thumbnail?.image?.[0]?.url ?? ''
      const badgeText =
        item.content_image?.primary_thumbnail?.overlays?.[0]?.badges?.[0]?.text ?? ''
      const videoCount = parseInt(badgeText) || -1

      return {
        type: 'playlist',
        title,
        playlistId,
        playlistThumbnail: thumbnailUrl,
        author,
        authorId,
        authorUrl,
        authorVerified: false,
        videoCount,
        videos: [],
      }
    })

  return { playlists }
}

type PlaylistVideo = {
  type: string
  title: { text: string }
  id: string
  author: { name: string; id: string }
  thumbnails: Array<{ url: string; width: number; height: number }>
  views: { text: string }
  published: { text: string }
  duration?: { text: string }
  video_info?: { runs: Array<{ text: string }> }
  is: (type: unknown) => boolean
}

const isPlaylistVideo = (v: unknown): v is PlaylistVideo => {
  if (!v || typeof v !== 'object') return false
  if (!('is' in v) || typeof v.is !== 'function') return false
  return (v as { is: (type: unknown) => boolean }).is(YTNodes.PlaylistVideo)
}

export function mapPlaylistVideo(video: any, playlist?: any): any {
  const videoType = (() => {
    if (typeof video !== 'object' || !video) return 'video'
    if ('type' in video && typeof video.type === 'string') {
      return video.type.replace('Grid', '').toLowerCase()
    }
    return 'video'
  })()

  if (isPlaylistVideo(video) && parseDuration(video.duration?.text) > 0) {
    return {
      type: videoType,
      title: video.title?.text || '',
      videoId: video.id || '',
      author: video.author.name || '',
      authorId: video.author.id || '',
      authorUrl: `/channel/${video.author.id}`,
      authorVerified: true,
      videoThumbnails: video.thumbnails || [],
      description: playlist?.info?.description || '',
      descriptionHtml: playlist?.info?.description || '',
      viewCount: parseCount(video.video_info?.runs[0].text.replace(/,/g, '') || '0'),
      viewCountText: video.video_info?.runs[0].text || '',
      published: parseRelativeTimeToTimestamp(video.video_info?.runs[2].text || '') || 0,
      publishedText: video.video_info?.runs[2].text || '',
      lengthSeconds: video.duration?.text ? parseDuration(video.duration.text) : 0,
    }
  }

  return null
}
