import { parseCount, parseRelativeTimeToTimestamp } from '../utils'

export function mapDataTrendingToNewFormat(data: DataTrending): NewDataTrending | null {
  if (!data.type.toLowerCase().includes('video')) return null

  const getBestThumbnail = (thumbnails: ThumbnailThumbnail[]): VideoThumbnail => {
    const sorted = [...thumbnails].sort((a, b) => b.width * b.height - a.width * a.height)
    return {
      quality: `${sorted[0].width}x${sorted[0].height}`,
      url: sorted[0].url,
      width: sorted[0].width,
      height: sorted[0].height,
    }
  }

  const getTextFromRuns = (runs?: Array<{ text: string }>, fallback = ''): string => {
    return runs?.map((run) => run.text).join('') || fallback
  }

  const parseDuration = (durationText?: Duration): number => {
    if (!durationText?.text) return 0
    const parts = durationText.text.split(':').map(Number).reverse()
    return parts.reduce((acc, val, i) => acc + val * Math.pow(60, i), 0)
  }

  return {
    type: data.type.toLowerCase() || 'video',
    title: data.title?.text || getTextFromRuns(data.title?.runs, ''),
    videoId: data.video_id || '',
    author: data.author?.name || '',
    authorId: data.author?.id || '',
    authorUrl: data.author?.url || '',
    authorVerified: data.author?.is_verified || false,
    authorThumbnails:
      data.author?.thumbnails?.map((t) => ({
        url: t.url,
        width: t.width,
        height: t.height,
      })) || [],
    videoThumbnails: data.thumbnails ? [getBestThumbnail(data.thumbnails)] : [],
    description: data.description_snippet?.text || '',
    descriptionHtml: getTextFromRuns(data.description_snippet?.runs, ''),
    viewCount: parseCount(data.view_count?.text),
    viewCountText: data.view_count?.text || '0',
    published: parseRelativeTimeToTimestamp(data.published?.text || '') || 0,
    publishedText: data.published?.text || '',
    lengthSeconds: parseDuration(data.duration),
  }
}

export type NewDataTrending = {
  type: string
  title: string
  videoId: string
  author: string
  authorId: string
  authorUrl: string
  authorVerified: boolean
  authorThumbnails: AuthorThumbnail[]
  videoThumbnails: VideoThumbnail[]
  description: string
  descriptionHtml: string
  viewCount: number
  viewCountText: string
  published: number
  publishedText: string
  lengthSeconds: number
  liveNow?: boolean
  premium?: boolean
  isUpcoming?: boolean
  isNew?: boolean
  is4k?: boolean
  is8k?: boolean
  isVr180?: boolean
  isVr360?: boolean
  is3d?: boolean
  hasCaptions?: boolean
}

export type VideoThumbnail = {
  quality: string
  url: string
  width: number
  height: number
}

export type DataTrending = {
  type: string
  title?: DatumTitle
  video_id?: string
  expandable_metadata?: null
  description_snippet?: DescriptionSnippet
  thumbnails?: ThumbnailThumbnail[]
  thumbnail_overlays?: ThumbnailOverlay[]
  author?: Author
  badges?: DatumBadge[]
  endpoint?: DatumEndpoint
  published?: Published
  view_count?: Published
  short_view_count?: Duration
  show_action_menu?: boolean
  is_watched?: boolean
  menu?: Menu
  length_text?: Duration
  rich_thumbnail?: RichThumbnail[]
  duration?: Duration
  views?: Published
  entity_id?: string
  accessibility_text?: string
  thumbnail?: ThumbnailThumbnail[]
  on_tap_endpoint?: OnTapEndpoint
  menu_on_tap?: MenuOnTap
  index_in_collection?: number
  menu_on_tap_a11y_label?: string
  overlay_metadata?: OverlayMetadata
}

export type Author = {
  id: string
  name: string
  thumbnails: AuthorThumbnail[]
  endpoint: AuthorEndpoint
  badges: AuthorBadge[]
  is_moderator?: boolean
  is_verified?: boolean
  is_verified_artist?: boolean
  url: string
}

export type AuthorBadge = {
  type: string
  icon_type: string
  style: string
  tooltip: string
}

export type AuthorEndpoint = {
  type: string
  command: OnTapEndpointCommand
  name: string
  payload: PurplePayload
  metadata: OnTapEndpointMetadata
}

export type OnTapEndpointCommand = {
  type: string
}

export type OnTapEndpointMetadata = {
  url: string
  page_type: string
  api_url: string
}

export type PurplePayload = {
  browseId: string
  canonicalBaseUrl: string
}

export type AuthorThumbnail = {
  url: string
  width: number
  height: number
}

export type DatumBadge = {
  type: string
  style: string
  label: string
}

export type DescriptionSnippet = {
  runs: DescriptionSnippetRun[]
  text: string
  rtl: boolean
}

export type DescriptionSnippetRun = {
  text: string
  bold: boolean
  bracket: boolean
  italics: boolean
  strikethrough: boolean
  error_underline: boolean
  underline: boolean
  deemphasize: boolean
}

export type Duration = {
  text: string
  accessibility: Accessibility
  rtl: boolean
}

export type Accessibility = {
  accessibility_data: AccessibilityData
}

export type AccessibilityData = {
  label: string
}

export type DatumEndpoint = {
  type: string
  command: OnTapEndpointCommand
  name: string
  payload: FluffyPayload
  metadata: OnTapEndpointMetadata
}

export type FluffyPayload = {
  videoId: string
  watchEndpointSupportedOnesieConfig: WatchEndpointSupportedOnesieConfig
  playerParams?: string
}

export type WatchEndpointSupportedOnesieConfig = {
  html5PlaybackOnesieConfig: Html5PlaybackOnesieConfig
}

export type Html5PlaybackOnesieConfig = {
  commonConfig: CommonConfig
}

export type CommonConfig = {
  url: string
}

export type Menu = {
  type: string
  items: Item[]
  accessibility: Accessibility
}

export type Item = {
  type: string
  text?: string
  icon_type?: string
  endpoint: ItemEndpoint
  has_separator?: boolean
}

export type ItemEndpoint = {
  type: string
  command?: UntoggledEndpointCommand
  name: string
  payload: TentacledPayload
  metadata: PurpleMetadata
}

export type UntoggledEndpointCommand = {
  type: string
  actions?: CommandAction[]
  signal?: string
}

export type CommandAction = {
  type: string
  open_miniplayer: boolean
  video_id: string
  list_type: string
  endpoint: ActionEndpoint
  video_ids: string[]
}

export type ActionEndpoint = {
  type: string
  command: OnTapEndpointCommand
  name: string
  payload: CreatePlaylistServiceEndpointClass
  metadata: ToggledEndpointMetadata
}

export type ToggledEndpointMetadata = {
  api_url: string
  send_post: boolean
}

export type CreatePlaylistServiceEndpointClass = {
  videoIds: string[]
  params: string
}

export type PurpleMetadata = {
  send_post?: boolean
  api_url?: string
}

export type TentacledPayload = {
  signal?: string
  actions?: PurpleAction[]
  serializedShareEntity?: string
  commands?: CommandElement[]
  videoId?: string
  onAddCommand?: OnAddCommand
}

export type PurpleAction = {
  addToPlaylistCommand: AddToPlaylistCommand
}

export type AddToPlaylistCommand = {
  openMiniplayer: boolean
  videoId: string
  listType: string
  onCreateListCommand: OnCreateListCommand
  videoIds: string[]
}

export type OnCreateListCommand = {
  clickTrackingParams: string
  commandMetadata: OnCreateListCommandCommandMetadata
  createPlaylistServiceEndpoint: CreatePlaylistServiceEndpointClass
}

export type OnCreateListCommandCommandMetadata = {
  webCommandMetadata: PurpleWebCommandMetadata
}

export type PurpleWebCommandMetadata = {
  sendPost: boolean
  apiUrl: string
}

export type CommandElement = {
  clickTrackingParams: string
  openPopupAction: OpenPopupAction
}

export type OpenPopupAction = {
  popup: Popup
  popupType: string
  beReused: boolean
}

export type Popup = {
  unifiedSharePanelRenderer: UnifiedSharePanelRenderer
}

export type UnifiedSharePanelRenderer = {
  trackingParams: string
  showLoadingSpinner: boolean
}

export type OnAddCommand = {
  clickTrackingParams: string
  getDownloadActionCommand: GetDownloadActionCommand
}

export type GetDownloadActionCommand = {
  videoId: string
  params: string
}

export type MenuOnTap = {
  type: string
  name: string
  payload: MenuOnTapPayload
  metadata: MenuOnTapMetadata
}

export type MenuOnTapMetadata = {}

export type MenuOnTapPayload = {
  panelLoadingStrategy: PanelLoadingStrategy
}

export type PanelLoadingStrategy = {
  inlineContent: InlineContent
}

export type InlineContent = {
  sheetViewModel: SheetViewModel
}

export type SheetViewModel = {
  content: Content
}

export type Content = {
  listViewModel: ListViewModel
}

export type ListViewModel = {
  listItems: ListItem[]
}

export type ListItem = {
  listItemViewModel: ListItemViewModel
}

export type ListItemViewModel = {
  title: ListItemViewModelTitle
  leadingImage: LeadingImage
  rendererContext: RendererContext
}

export type LeadingImage = {
  sources: Source[]
}

export type Source = {
  clientResource: ClientResource
}

export type ClientResource = {
  imageName: string
}

export type RendererContext = {
  loggingContext?: RendererContextLoggingContext
  commandContext: CommandContext
}

export type CommandContext = {
  onTap: OnTap
}

export type OnTap = {
  innertubeCommand: InnertubeCommand
}

export type InnertubeCommand = {
  clickTrackingParams: string
  commandMetadata: InnertubeCommandCommandMetadata
  signalServiceEndpoint?: SignalServiceEndpoint
  userFeedbackEndpoint?: UserFeedbackEndpoint
}

export type InnertubeCommandCommandMetadata = {
  webCommandMetadata: FluffyWebCommandMetadata
}

export type FluffyWebCommandMetadata = {
  sendPost?: boolean
  ignoreNavigation?: boolean
}

export type SignalServiceEndpoint = {
  signal: string
  actions: SignalServiceEndpointAction[]
}

export type SignalServiceEndpointAction = {
  clickTrackingParams: string
  addToPlaylistCommand: AddToPlaylistCommand
}

export type UserFeedbackEndpoint = {
  additionalDatas: AdditionalData[]
}

export type AdditionalData = {
  userFeedbackEndpointProductSpecificValueData: UserFeedbackEndpointProductSpecificValueData
}

export type UserFeedbackEndpointProductSpecificValueData = {
  key: string
  value: string
}

export type RendererContextLoggingContext = {
  loggingDirectives: LoggingDirectives
}

export type LoggingDirectives = {
  trackingParams: string
}

export type ListItemViewModelTitle = {
  content: string
}

export type OnTapEndpoint = {
  type: string
  command: OnTapEndpointCommand
  name: string
  payload: OnTapEndpointPayload
  metadata: OnTapEndpointMetadata
}

export type OnTapEndpointPayload = {
  videoId: string
  playerParams: string
  thumbnail: PayloadThumbnail
  overlay: Overlay
  params: string
  sequenceProvider: string
  sequenceParams: string
  loggingContext: PayloadLoggingContext
  ustreamerConfig: string
  identifier: string
}

export type PayloadLoggingContext = {
  vssLoggingContext: LoggingContext
  qoeLoggingContext: LoggingContext
}

export type LoggingContext = {
  serializedContextData: string
}

export type Overlay = {
  reelPlayerOverlayRenderer: ReelPlayerOverlayRenderer
}

export type ReelPlayerOverlayRenderer = {
  style: string
  trackingParams: string
  reelPlayerNavigationModel: string
}

export type PayloadThumbnail = {
  thumbnails: ThumbnailThumbnail[]
  isOriginalAspectRatio: boolean
}

export type ThumbnailThumbnail = {
  url: string
  width: number
  height: number
}

export type OverlayMetadata = {
  primary_text: AryText
  secondary_text: AryText
}

export type AryText = {
  runs: PrimaryTextRun[]
  text: string
  rtl: boolean
}

export type PrimaryTextRun = {
  text: string
  bold: boolean
  bracket: boolean
  italics: boolean
  strikethrough: boolean
  error_underline: boolean
  underline: boolean
  deemphasize: boolean
}

export type Published = {
  text: string
  rtl: boolean
}

export type RichThumbnail = {
  url: string
  width: number
  height: number
}

export type ThumbnailOverlay = {
  type: string
  text?: string
  style?: string
  is_toggled?: boolean
  icon_type?: IconType
  tooltip?: IconType
  toggled_endpoint?: ToggledEndpoint
  untoggled_endpoint?: UntoggledEndpoint
}

export type IconType = {
  toggled: string
  untoggled: string
}

export type ToggledEndpoint = {
  type: string
  command: OnTapEndpointCommand
  name: string
  payload: ToggledEndpointPayload
  metadata: ToggledEndpointMetadata
}

export type ToggledEndpointPayload = {
  playlistId: string
  actions: FluffyAction[]
}

export type FluffyAction = {
  action: string
  removedVideoId: string
}

export type UntoggledEndpoint = {
  type: string
  command: UntoggledEndpointCommand
  name: string
  payload: UntoggledEndpointPayload
  metadata: UntoggledEndpointMetadata
}

export type UntoggledEndpointMetadata = {
  api_url?: string
  send_post: boolean
}

export type UntoggledEndpointPayload = {
  playlistId?: string
  actions: TentacledAction[]
  signal?: string
}

export type TentacledAction = {
  addedVideoId?: string
  action?: string
  addToPlaylistCommand?: AddToPlaylistCommand
}

export type DatumTitle = {
  runs: PrimaryTextRun[]
  text: string
  accessibility: Accessibility
  rtl: boolean
}
