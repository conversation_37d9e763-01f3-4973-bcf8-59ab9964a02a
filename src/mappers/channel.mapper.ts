import { YTNodes } from 'youtubei.js/cf-worker'

import { parseCount, parseDuration, parseRelativeTimeToTimestamp } from '../utils'

import type { ChannelReturn } from '../types'

type ChannelVideo = {
  type: string
  title: string
  videoId: string
  author: string
  authorId: string
  authorUrl: string
  authorVerified: boolean
  videoThumbnails: Array<{
    url: string
    width: number
    height: number
  }>
  description: string
  descriptionHtml: string
  viewCount: number
  viewCountText: string
  published: number
  publishedText: string
  lengthSeconds: number
}

type ChannelInfo = {
  title: string
  external_id: string
  url: string
  thumbnail?: Array<{
    url: string
    width: number
    height: number
  }>
}

type GridVideo = {
  type: string
  title: { text: string }
  video_id: string
  thumbnails: Array<{ url: string; width: number; height: number }>
  views: { text: string }
  published: { text: string }
  duration?: { text: string }
  is: (type: unknown) => boolean
}

const isGridVideo = (v: unknown): v is GridVideo => {
  if (!v || typeof v !== 'object') return false
  if (!('is' in v) || typeof v.is !== 'function') return false
  return (v as { is: (type: unknown) => boolean }).is(YTNodes.GridVideo)
}

export function mapChannelVideo(video: unknown, channelInfo?: ChannelInfo): ChannelVideo | null {
  const videoType = (() => {
    if (typeof video !== 'object' || !video) return 'video'
    if ('type' in video && typeof video.type === 'string') {
      return video.type.replace('Grid', '').toLowerCase()
    }
    return 'video'
  })()

  if (isGridVideo(video) && parseDuration(video.duration?.text) > 0) {
    return {
      type: videoType,
      title: video.title?.text || '',
      videoId: video.video_id || '',
      author: channelInfo?.title || '',
      authorId: channelInfo?.external_id || '',
      authorUrl: `/channel/${channelInfo?.external_id}`,
      authorVerified: true,
      videoThumbnails: video.thumbnails || [],
      description: '',
      descriptionHtml: '',
      viewCount: parseCount(video.views?.text),
      viewCountText: video.views?.text || '',
      published: parseRelativeTimeToTimestamp(video?.published?.text || '') || 0,
      publishedText: video.published?.text || '',
      lengthSeconds: video.duration?.text ? parseDuration(video.duration.text) : 0,
    }
  }

  return null
}

export function mapChannelResponse(channel: ChannelReturn) {
  const channelInfo = {
    title: channel.metadata.title || '',
    external_id: channel.metadata.external_id || '',
    url: channel.metadata.url || '',
    thumbnail: channel.metadata.thumbnail || [],
  }

  let authorBanners
  let subCount

  if (channel.header?.is(YTNodes.PageHeader)) {
    authorBanners = channel.header?.content?.banner?.image
    subCount = channel.header?.content?.metadata?.metadata_rows[1]?.metadata_parts?.[0].text?.text
  }

  return {
    author: channelInfo.title,
    authorId: channelInfo.external_id,
    authorUrl: `/channel/${channelInfo.external_id}`,
    authorBanners,
    authorThumbnails: channelInfo.thumbnail,
    subCount: parseCount(subCount),
    autoGenerated: false,
    ageGated: false,
    isFamilyFriendly: true,
    description: channel.metadata.description || '',
    descriptionHtml: channel.metadata.description || '',
    tabs: channel.tabs?.map((tab) => tab.toLowerCase()) || [],
    allowedRegions: channel.metadata.available_countries || [],
    tags: channel.metadata.tags || [],
    latestVideos: channel.videos?.map((video) => mapChannelVideo(video, channelInfo)) || [],
    relatedChannels: channel.channels || [],
  }
}
