import { parseCount, parseRelativeTimeToTimestamp } from '../utils'

export type DataSearch = {
  header: DataSearchHeader
  results: Result[]
  refinements: string[]
  estimated_results: number
  sub_menu: SubMenu
  watch_card: WatchCard
}

export type DataSearchHeader = {
  type: string
  chip_bar: ChipBar
  search_filter_button: SearchFilterButton
}

export type ChipBar = {
  type: string
  chips: Chip[]
  next_button: ChipBarNextButton
  previous_button: ChipBarNextButton
}

export type Chip = {
  type: string
  is_selected: boolean
  text: string
  endpoint?: ChipEndpoint
}

export type ChipEndpoint = {
  type: string
  command: SubMenu
  name: string
  payload: PurplePayload
  metadata: ToggledEndpointMetadata
}

export type SubMenu = {
  type: string
}

export type ToggledEndpointMetadata = {
  api_url: string
  send_post: boolean
}

export type PurplePayload = {
  token: string
  request: string
  command: PurpleCommand
}

export type PurpleCommand = {
  clickTrackingParams: string
  showReloadUiCommand: ShowReloadUICommand
}

export type ShowReloadUICommand = {
  targetId: string
}

export type ChipBarNextButton = {
  type: string
  label: string
  style: string
  size: string
  icon_type: string
  is_disabled: boolean
  endpoint: ViewAllEndpointClass
}

export type ViewAllEndpointClass = {
  type: string
  payload: PayloadClass
  metadata: PayloadClass
}

export type PayloadClass = {}

export type SearchFilterButton = {
  type: string
  text: string
  accessibility: Accessibility
  tooltip: string
  style: string
  size: string
  icon_type: string
  is_disabled: boolean
  endpoint: SearchFilterButtonEndpoint
}

export type Accessibility = {
  accessibility_data: AccessibilityData
}

export type AccessibilityData = {
  label: string
}

export type SearchFilterButtonEndpoint = {
  type: string
  command: OpenPopupClass
  open_popup: OpenPopupClass
  payload: PayloadClass
  metadata: PayloadClass
}

export type OpenPopupClass = {
  type: string
  popup: CommandPopup
  popup_type: string
}

export type CommandPopup = {
  type: string
  title: CollapsedLabel
  groups: Group[]
}

export type Group = {
  type: string
  title: Published
  filters: Filter[]
}

export type Filter = {
  type: string
  label: Published
  endpoint: FilterEndpoint
  tooltip: string
  status?: string
}

export type FilterEndpoint = {
  type: string
  command?: SubMenu
  name?: string
  payload: FluffyPayload
  metadata: PurpleMetadata
}

export type PurpleMetadata = {
  url?: string
  page_type?: string
  api_url?: string
}

export type FluffyPayload = {
  query?: string
  params?: string
}

export type Published = {
  text: string
  rtl: boolean
}

export type CollapsedLabel = {
  runs: CollapsedLabelRun[]
  text: string
  rtl: boolean
}

export type CollapsedLabelRun = {
  text: string
  bold: boolean
  bracket: boolean
  italics: boolean
  strikethrough: boolean
  error_underline: boolean
  underline: boolean
  deemphasize: boolean
}

export type Result = {
  type: string
  title?: ResultTitle
  video_id?: string
  expandable_metadata?: null
  snippets?: Snippet[]
  thumbnails?: BottomRightElement[]
  thumbnail_overlays?: ResultThumbnailOverlay[]
  rich_thumbnail?: RichThumbnail[]
  author?: ResultAuthor
  badges?: ResultBadge[]
  endpoint?: ResultEndpoint
  published?: Published
  view_count?: Published
  short_view_count?: LengthText
  show_action_menu?: boolean
  is_watched?: boolean
  menu?: Menu
  search_video_result_entity_key?: string
  length_text?: LengthText
  content_image?: ContentImage
  metadata?: ResultMetadata
  content_id?: string
  content_type?: string
  renderer_context?: RendererContextClass
  id?: string
  subscriber_count?: Published
  video_count?: LengthText
  long_byline?: Byline
  short_byline?: Byline
  subscribe_button?: SubscribeButton
  description_snippet?: CollapsedLabel
  content?: ResultContent
  items?: ResultItem[]
}

export type ResultAuthor = {
  id: string
  name: string
  thumbnails: LeftElement[]
  endpoint: LongBylineEndpoint
  badges: AuthorBadge[]
  is_moderator?: boolean
  is_verified?: boolean
  is_verified_artist?: boolean
  url: string
}

export type AuthorBadge = {
  type: string
  icon_type: string
  style: string
  tooltip: string
}

export type LongBylineEndpoint = {
  type: string
  command: SubMenu
  name: string
  payload: TentacledPayload
  metadata: OnTapEndpointMetadata
}

export type OnTapEndpointMetadata = {
  url: string
  page_type: string
  api_url: string
}

export type TentacledPayload = {
  browseId: string
  canonicalBaseUrl: string
}

export type LeftElement = {
  url: string
  width: number
  height: number
}

export type ResultBadge = {
  type: string
  style: string
  label: string
}

export type ResultContent = {
  type: string
  visible_item_count?: number
  items: ContentItem[]
  collapsed_item_count?: number
  collapsed_state_button_text?: CollapsedStateButtonText
}

export type CollapsedStateButtonText = {
  runs: CollapsedLabelRun[]
  text: string
  accessibility: Accessibility
  rtl: boolean
}

export type ContentItem = {
  type: string
  video_id: string
  title: CollapsedStateButtonText
  thumbnails: BottomRightElement[]
  thumbnail_overlays: ItemThumbnailOverlay[]
  rich_thumbnail: RichThumbnail[]
  published: Published
  duration?: LengthText
  author: ItemAuthor
  views?: Published
  short_view_count: LengthText
  endpoint: PurpleEndpoint
  menu: Menu
  expandable_metadata?: ExpandableMetadata | null
  snippets?: Snippet[]
  badges?: ResultBadge[]
  view_count?: Published
  show_action_menu?: boolean
  is_watched?: boolean
  search_video_result_entity_key?: string
  length_text?: LengthText
}

export type ItemAuthor = {
  id: string
  name: string
  thumbnails: LeftElement[]
  endpoint: LongBylineEndpoint
  badges: AuthorBadge[]
  is_moderator?: boolean
  is_verified?: boolean
  is_verified_artist?: boolean
  url: string
}

export type LengthText = {
  text: string
  accessibility: Accessibility
  rtl: boolean
}

export type PurpleEndpoint = {
  type: string
  command: SubMenu
  name: string
  payload: StickyPayload
  metadata: OnTapEndpointMetadata
}

export type StickyPayload = {
  videoId: string
  params: string
  playerParams: string
  watchEndpointSupportedOnesieConfig: WatchEndpointSupportedOnesieConfig
  startTimeSeconds?: number
}

export type WatchEndpointSupportedOnesieConfig = {
  html5PlaybackOnesieConfig: Html5PlaybackOnesieConfig
}

export type Html5PlaybackOnesieConfig = {
  commonConfig: CommonConfig
}

export type CommonConfig = {
  url: string
}

export type ExpandableMetadata = {
  type: string
  header: ExpandableMetadataHeader
  expanded_content: ExpandedContent
  expand_button: CollapseButtonClass
  collapse_button: CollapseButtonClass
}

export type CollapseButtonClass = {
  type: string
  accessibility: Accessibility
  style: string
  size: string
  icon_type: string
  is_disabled: boolean
  endpoint: ViewAllEndpointClass
}

export type ExpandedContent = {
  type: string
  cards: ExpandedContentCard[]
  header: null
  previous_button: ExpandedContentNextButton
  next_button: ExpandedContentNextButton
}

export type ExpandedContentCard = {
  type: string
  title: CollapsedLabel
  time_description: CollapsedLabel
  thumbnail: BottomRightElement[]
  on_tap_endpoint: CardOnTapEndpoint
  layout: string
  is_highlighted: boolean
}

export type CardOnTapEndpoint = {
  type: string
  command: SubMenu
  name: string
  payload: IndigoPayload
  metadata: OnTapEndpointMetadata
}

export type IndigoPayload = {
  videoId: string
  playerParams?: string
  watchEndpointSupportedOnesieConfig: WatchEndpointSupportedOnesieConfig
  startTimeSeconds?: number
}

export type BottomRightElement = {
  url: string
  width: number
  height: number
}

export type ExpandedContentNextButton = {
  type: string
  style: string
  size: string
  icon_type: string
  is_disabled: boolean
  endpoint: ViewAllEndpointClass
}

export type ExpandableMetadataHeader = {
  collapsed_title: CollapsedLabel
  collapsed_thumbnail: BottomRightElement[]
  collapsed_label: CollapsedLabel
  expanded_title: CollapsedLabel
}

export type Menu = {
  type: string
  items: MenuItem[]
  accessibility: Accessibility
}

export type MenuItem = {
  type: string
  text?: string
  icon_type?: string
  endpoint: FluffyEndpoint
  has_separator?: boolean
}

export type FluffyEndpoint = {
  type: string
  command?: UntoggledEndpointCommand
  name: string
  payload: IndecentPayload
  metadata: FluffyMetadata
}

export type UntoggledEndpointCommand = {
  type: string
  actions?: CommandAction[]
  signal?: string
}

export type CommandAction = {
  type: string
  open_miniplayer: boolean
  video_id: string
  list_type: string
  endpoint: ActionEndpoint
  video_ids: string[]
}

export type ActionEndpoint = {
  type: string
  command: SubMenu
  name: string
  payload: CreatePlaylistServiceEndpointClass
  metadata: ToggledEndpointMetadata
}

export type CreatePlaylistServiceEndpointClass = {
  videoIds: string[]
  params: string
}

export type FluffyMetadata = {
  send_post?: boolean
  api_url?: string
}

export type IndecentPayload = {
  signal?: string
  actions?: PurpleAction[]
  serializedShareEntity?: string
  commands?: CommandElement[]
  videoId?: string
  onAddCommand?: OnAddCommand
}

export type PurpleAction = {
  addToPlaylistCommand: PurpleAddToPlaylistCommand
}

export type PurpleAddToPlaylistCommand = {
  openMiniplayer: boolean
  videoId: string
  listType: string
  onCreateListCommand: OnCreateListCommand
  videoIds: string[]
  videoCommand: VideoCommand
}

export type OnCreateListCommand = {
  clickTrackingParams: string
  commandMetadata: OnCreateListCommandCommandMetadata
  createPlaylistServiceEndpoint: CreatePlaylistServiceEndpointClass
}

export type OnCreateListCommandCommandMetadata = {
  webCommandMetadata: PurpleWebCommandMetadata
}

export type PurpleWebCommandMetadata = {
  sendPost: boolean
  apiUrl: string
}

export type VideoCommand = {
  clickTrackingParams: string
  commandMetadata: VideoCommandCommandMetadata
  watchEndpoint: WatchEndpoint
}

export type VideoCommandCommandMetadata = {
  webCommandMetadata: FluffyWebCommandMetadata
}

export type FluffyWebCommandMetadata = {
  url: string
  webPageType: string
  rootVe: number
}

export type WatchEndpoint = {
  videoId: string
  watchEndpointSupportedOnesieConfig: WatchEndpointSupportedOnesieConfig
  playerParams?: string
}

export type CommandElement = {
  clickTrackingParams: string
  openPopupAction: OpenPopupAction
}

export type OpenPopupAction = {
  popup: OpenPopupActionPopup
  popupType: string
  beReused: boolean
}

export type OpenPopupActionPopup = {
  unifiedSharePanelRenderer: UnifiedSharePanelRenderer
}

export type UnifiedSharePanelRenderer = {
  trackingParams: string
  showLoadingSpinner: boolean
}

export type OnAddCommand = {
  clickTrackingParams: string
  getDownloadActionCommand: GetDownloadActionCommand
}

export type GetDownloadActionCommand = {
  videoId: string
  params: string
}

export type RichThumbnail = {
  url: string
  width: number
  height: number
}

export type Snippet = {
  text: CollapsedLabel
  hover_text: CollapsedLabel
}

export type ItemThumbnailOverlay = {
  type: string
  text?: CollapsedLabel | string
  style?: string
  is_toggled?: boolean
  icon_type?: IconType
  tooltip?: IconType
  toggled_endpoint?: ToggledEndpoint
  untoggled_endpoint?: UntoggledEndpoint
  percent_duration_watched?: number
}

export type IconType = {
  toggled: string
  untoggled: string
}

export type ToggledEndpoint = {
  type: string
  command: SubMenu
  name: string
  payload: ToggledEndpointPayload
  metadata: ToggledEndpointMetadata
}

export type ToggledEndpointPayload = {
  playlistId: string
  actions: FluffyAction[]
}

export type FluffyAction = {
  action: string
  removedVideoId: string
}

export type UntoggledEndpoint = {
  type: string
  command: UntoggledEndpointCommand
  name: string
  payload: UntoggledEndpointPayload
  metadata: UntoggledEndpointMetadata
}

export type UntoggledEndpointMetadata = {
  api_url?: string
  send_post: boolean
}

export type UntoggledEndpointPayload = {
  playlistId?: string
  actions: TentacledAction[]
  signal?: string
}

export type TentacledAction = {
  addedVideoId?: string
  action?: string
  addToPlaylistCommand?: FluffyAddToPlaylistCommand
}

export type FluffyAddToPlaylistCommand = {
  openMiniplayer: boolean
  videoId: string
  listType: string
  onCreateListCommand: OnCreateListCommand
  videoIds: string[]
}

export type ContentImage = {
  type: string
  primary_thumbnail: PrimaryThumbnail
}

export type PrimaryThumbnail = {
  type: string
  image: BottomRightElement[]
  overlays: OverlayElement[]
}

export type OverlayElement = {
  type: string
  badges?: OverlayBadge[]
  position?: string
  icon_name?: string
  text?: CollapsedLabel
  style?: string
}

export type OverlayBadge = {
  type: string
  text: string
  badge_style: string
}

export type ResultEndpoint = {
  type: string
  command: SubMenu
  name: string
  payload: HilariousPayload
  metadata: OnTapEndpointMetadata
}

export type HilariousPayload = {
  videoId?: string
  params?: string
  playerParams?: string
  watchEndpointSupportedOnesieConfig?: WatchEndpointSupportedOnesieConfig
  browseId?: string
  canonicalBaseUrl?: string
}

export type ResultItem = {
  type: string
  entity_id: string
  accessibility_text: string
  thumbnail: BottomRightElement[]
  on_tap_endpoint: ItemOnTapEndpoint
  menu_on_tap: MenuOnTap
  index_in_collection: number
  menu_on_tap_a11y_label: string
  overlay_metadata: OverlayMetadata
  inline_player_data: InlinePlayerData
  badge?: PurpleBadge
}

export type PurpleBadge = {
  type: string
  text: string
  style: string
  accessibility_label: string
}

export type InlinePlayerData = {
  type: string
  command: SubMenu
  name: string
  payload: InlinePlayerDataPayload
  metadata: OnTapEndpointMetadata
}

export type InlinePlayerDataPayload = {
  videoId: string
  playerParams: string
  playerExtraUrlParams: PlayerExtraURLParam[]
  watchEndpointSupportedOnesieConfig: WatchEndpointSupportedOnesieConfig
}

export type PlayerExtraURLParam = {
  key: string
  value: string
}

export type MenuOnTap = {
  type: string
  name: string
  payload: MenuOnTapPayload
  metadata: PayloadClass
}

export type MenuOnTapPayload = {
  panelLoadingStrategy: PanelLoadingStrategy
}

export type PanelLoadingStrategy = {
  inlineContent: InlineContent
}

export type InlineContent = {
  sheetViewModel: SheetViewModel
}

export type SheetViewModel = {
  content: SheetViewModelContent
}

export type SheetViewModelContent = {
  listViewModel: ListViewModel
}

export type ListViewModel = {
  listItems: ListItemElement[]
}

export type ListItemElement = {
  listItemViewModel: ListItemViewModel
}

export type ListItemViewModel = {
  title: ListItemViewModelTitle
  leadingImage: LeadingImage
  rendererContext: RendererContext
}

export type LeadingImage = {
  sources: Source[]
}

export type Source = {
  clientResource: ClientResource
}

export type ClientResource = {
  imageName: string
}

export type RendererContext = {
  loggingContext?: RendererContextLoggingContext
  commandContext: CommandContext
}

export type CommandContext = {
  onTap: OnTap
}

export type OnTap = {
  innertubeCommand: InnertubeCommand
}

export type InnertubeCommand = {
  clickTrackingParams: string
  commandMetadata: InnertubeCommandCommandMetadata
  signalServiceEndpoint?: SignalServiceEndpoint
  userFeedbackEndpoint?: UserFeedbackEndpoint
}

export type InnertubeCommandCommandMetadata = {
  webCommandMetadata: TentacledWebCommandMetadata
}

export type TentacledWebCommandMetadata = {
  sendPost?: boolean
  ignoreNavigation?: boolean
}

export type SignalServiceEndpoint = {
  signal: string
  actions: SignalServiceEndpointAction[]
}

export type SignalServiceEndpointAction = {
  clickTrackingParams: string
  addToPlaylistCommand: PurpleAddToPlaylistCommand
}

export type UserFeedbackEndpoint = {
  additionalDatas: AdditionalData[]
}

export type AdditionalData = {
  userFeedbackEndpointProductSpecificValueData: PlayerExtraURLParam
}

export type RendererContextLoggingContext = {
  loggingDirectives: LoggingDirectives
}

export type LoggingDirectives = {
  trackingParams: string
}

export type ListItemViewModelTitle = {
  content: string
}

export type ItemOnTapEndpoint = {
  type: string
  command: SubMenu
  name: string
  payload: AmbitiousPayload
  metadata: OnTapEndpointMetadata
}

export type AmbitiousPayload = {
  videoId: string
  playerParams: string
  thumbnail: PayloadThumbnail
  overlay: PayloadOverlay
  params: string
  sequenceProvider: string
  sequenceParams: string
  loggingContext: PurpleLoggingContext
  ustreamerConfig: string
  identifier: string
}

export type PurpleLoggingContext = {
  vssLoggingContext: LoggingContext
  qoeLoggingContext: LoggingContext
}

export type LoggingContext = {
  serializedContextData: string
}

export type PayloadOverlay = {
  reelPlayerOverlayRenderer: ReelPlayerOverlayRenderer
}

export type ReelPlayerOverlayRenderer = {
  style: string
  trackingParams: string
  reelPlayerNavigationModel: string
}

export type PayloadThumbnail = {
  thumbnails: BottomRightElement[]
  isOriginalAspectRatio: boolean
}

export type OverlayMetadata = {
  primary_text: CollapsedLabel
  secondary_text: CollapsedLabel
}

export type Byline = {
  runs: LongBylineRun[]
  text: string
  rtl: boolean
  endpoint: LongBylineEndpoint
}

export type LongBylineRun = {
  text: string
  bold: boolean
  bracket: boolean
  italics: boolean
  strikethrough: boolean
  error_underline: boolean
  underline: boolean
  deemphasize: boolean
  endpoint: LongBylineEndpoint
}

export type ResultMetadata = {
  type: string
  title: CollapsedLabel
  metadata: MetadataMetadata
  image: null
  menu_button: null
}

export type MetadataMetadata = {
  type: string
  metadata_rows: MetadataRow[]
  delimiter: string
}

export type MetadataRow = {
  metadata_parts?: MetadataPart[]
}

export type MetadataPart = {
  text: CollapsedLabel
  avatar_stack: null
}

export type RendererContextClass = {
  command_context: CommandContextClass
}

export type CommandContextClass = {
  on_tap: OnTapClass
}

export type OnTapClass = {
  type: string
  command: SubMenu
  name: string
  payload: OnTapPayload
  metadata: OnTapEndpointMetadata
}

export type OnTapPayload = {
  videoId: string
  playlistId: string
  params: string
  continuePlayback: boolean
  loggingContext: FluffyLoggingContext
  watchEndpointSupportedOnesieConfig: WatchEndpointSupportedOnesieConfig
}

export type FluffyLoggingContext = {
  vssLoggingContext: LoggingContext
}

export type SubscribeButton = {
  type: string
  text: string
  style: string
  size: string
  is_disabled: boolean
  endpoint: SubscribeButtonEndpoint
}

export type SubscribeButtonEndpoint = {
  type: string
  name: string
  payload: CunningPayload
  next_endpoint: NextEndpoint
  metadata: TentacledMetadata
}

export type TentacledMetadata = {
  url: string
  page_type: string
}

export type NextEndpoint = {
  type: string
  command: SubMenu
  name: string
  payload: SearchEndpointClass
  metadata: OnTapEndpointMetadata
}

export type SearchEndpointClass = {
  query: string
  params: string
}

export type CunningPayload = {
  nextEndpoint: NextEndpointClass
  continueAction: string
}

export type NextEndpointClass = {
  clickTrackingParams: string
  commandMetadata: VideoCommandCommandMetadata
  searchEndpoint: SearchEndpointClass
}

export type ResultThumbnailOverlay = {
  type: string
  text?: CollapsedLabel | string
  style?: string
  is_toggled?: boolean
  icon_type?: IconType
  tooltip?: IconType
  toggled_endpoint?: ToggledEndpoint
  untoggled_endpoint?: UntoggledEndpoint
}

export type ResultTitle = {
  runs?: CollapsedLabelRun[]
  text: string
  accessibility?: Accessibility
  rtl: boolean
}

export type WatchCard = {
  type: string
  header: WatchCardHeader
  call_to_action: CallToAction
  sections: Section[]
  collapsed_label: CollapsedLabel
}

export type CallToAction = {
  type: string
  endpoint: CallToActionEndpoint
  call_to_action_button: CallToActionButton
  hero_image: HeroImage
  label: string
}

export type CallToActionButton = {
  type: string
  label: Published
  icon_type: string
  style: string
}

export type CallToActionEndpoint = {
  type: string
  command: SubMenu
  name: string
  payload: MagentaPayload
  metadata: OnTapEndpointMetadata
}

export type MagentaPayload = {
  videoId: string
  playlistId: string
  params: string
  loggingContext: FluffyLoggingContext
  watchEndpointSupportedOnesieConfig: WatchEndpointSupportedOnesieConfig
}

export type HeroImage = {
  type: string
  left: LeftElement[]
  top_right: BottomRightElement[]
  bottom_right: BottomRightElement[]
  endpoint: ViewAllEndpointClass
}

export type WatchCardHeader = {
  type: string
  title: Published
  title_endpoint: TitleEndpointClass
  subtitle: Published
  author: HeaderAuthor
  style: string
}

export type HeaderAuthor = {
  id: string
  name: string
  thumbnails: LeftElement[]
  endpoint: TitleEndpointClass
  badges: AuthorBadge[]
  is_moderator: boolean
  is_verified: boolean
  is_verified_artist: boolean
  url: string
}

export type TitleEndpointClass = {
  type: string
  command: SubMenu
  name: string
  payload: TitleEndpointPayload
  metadata: OnTapEndpointMetadata
}

export type TitleEndpointPayload = {
  browseId: string
}

export type Section = {
  type: string
  lists: List[]
}

export type List = {
  type: string
  items?: ListItem[]
  view_all_text?: ViewAllText
  view_all_endpoint?: ViewAllEndpointClass
  cards?: ListCard[]
  header?: ListHeader
  previous_button?: ExpandedContentNextButton
  next_button?: ExpandedContentNextButton
}

export type ListCard = {
  type: string
  thumbnails: BottomRightElement[]
  endpoint: CardEndpoint
  query: string
}

export type CardEndpoint = {
  type: string
  name: string
  payload: FriskyPayload
  metadata: OnTapEndpointMetadata
}

export type FriskyPayload = {
  playlistId: string
}

export type ListHeader = {
  type: string
  title: LengthText
}

export type ListItem = {
  type: string
  title: Published
  subtitle: Published
  duration: Duration
  style: string
}

export type Duration = {
  text: string
  seconds: number
}

export type ViewAllText = {
  rtl: boolean
}

export type NewDataSearch = {
  type: string
  title?: string
  videoId?: string
  author: string
  authorId: string
  authorUrl: string
  authorVerified: boolean
  authorThumbnails?: AuthorThumbnail[]
  videoThumbnails?: VideoThumbnail[]
  description?: string
  descriptionHtml?: string
  viewCount?: number
  viewCountText?: string
  shortViewCount?: number
  shortViewCountText?: string
  published?: number
  publishedText?: string
  lengthSeconds?: number
  lengthText?: string
  liveNow?: boolean
  premium?: boolean
  isUpcoming?: boolean
  isNew?: boolean
  is4k?: boolean
  is8k?: boolean
  isVr180?: boolean
  isVr360?: boolean
  is3d?: boolean
  hasCaptions?: boolean
  playlistId?: string
  playlistThumbnail?: string
  videoCount?: number
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  videos?: any[]
  autoGenerated?: boolean
  subCount?: number
  channelHandle?: string
  thumbnailOverlays?: ResultThumbnailOverlay[]
}

export type AuthorThumbnail = {
  url: string
  width: number
  height: number
}

export type VideoThumbnail = {
  quality: string
  url: string
  width: number
  height: number
}

export function mapDataSearchToNewFormat(data: DataSearch): NewDataSearch[] {
  if (!data?.results?.length) return []

  return data.results
    .filter((result) => {
      // Only include video and playlist results
      const type = result.type?.toLowerCase()
      return type === 'video' || type === 'playlist' || type === 'channel'
    })
    .map((result) => {
      const baseData: Partial<NewDataSearch> = {
        type: result.type?.toLowerCase() || 'video',
        title: result.title?.text || '',
        videoId: result.video_id || '',
        author: result.author?.name || '',
        authorId: result.author?.id || '',
        authorUrl: `/channel/${result.author?.id}`,
        authorVerified: result.author?.is_verified || false,
        viewCountText: result.view_count?.text || '0',
        published: parseRelativeTimeToTimestamp(result.published?.text || '') || 0,
        publishedText: result.published?.text || '',
        thumbnailOverlays: result.thumbnail_overlays,
        shortViewCountText: result.short_view_count?.text || '',
      }

      if (result.thumbnails?.length) {
        const bestThumbnail = [...result.thumbnails].sort(
          (a, b) => b.width * b.height - a.width * a.height,
        )[0]

        baseData.videoThumbnails = [
          {
            quality: `${bestThumbnail.width}x${bestThumbnail.height}`,
            url: bestThumbnail.url,
            width: bestThumbnail.width,
            height: bestThumbnail.height,
          },
        ]
      }

      // Handle author thumbnails
      if (result.author?.thumbnails?.length) {
        baseData.authorThumbnails = result.author.thumbnails.map((t) => ({
          url: t.url,
          width: t.width,
          height: t.height,
        }))
      }

      // Handle description
      if (result.description_snippet) {
        baseData.description = result.description_snippet.text || ''
        baseData.descriptionHtml =
          result.description_snippet.runs?.map((run) => run.text).join('') || ''
      }

      // Parse view count
      if (result.view_count?.text) {
        const viewCount = parseInt(result.view_count.text.replace(/\D/g, ''))
        if (!isNaN(viewCount)) {
          baseData.viewCount = viewCount
        }
      }

      // Parse published timestamp
      if (result.published?.text) {
        const date = new Date(result.published.text)
        if (!isNaN(date.getTime())) {
          baseData.published = Math.floor(date.getTime() / 1000)
        }
      }

      // Parse duration
      if (result.length_text?.text) {
        baseData.lengthText = result.length_text.text
        const parts = result.length_text.text.split(':').reverse()
        baseData.lengthSeconds = parts.reduce(
          (acc, val, i) => acc + (parseInt(val) || 0) * Math.pow(60, i),
          0,
        )
      }

      // Handle playlists
      if (result.type?.toLowerCase() === 'playlist') {
        baseData.playlistId = result.id
        baseData.videoCount = result.video_count
          ? parseInt(result.video_count.text.replace(/\D/g, ''))
          : 0

        if (result.thumbnails?.[0]?.url) {
          baseData.playlistThumbnail = result.thumbnails[0].url
        }
      }

      // Handle channels
      if (result.type?.toLowerCase() === 'channel') {
        baseData.subCount = parseCount(result.video_count?.text)

        baseData.videoCount = parseCount(result.subscriber_count?.text)

        baseData.channelHandle = result.author?.url?.split('@').pop() || ''
      }

      return baseData as NewDataSearch
    })
}
