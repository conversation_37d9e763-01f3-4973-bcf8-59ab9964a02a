import { parseDuration, parseRelativeTimeToTimestamp } from '../utils'

type VideoFormat = {
  itag?: number | string
  mimeType?: string
  bitrate?: number
  width?: number
  height?: number
  lastModified?: string
  contentLength?: string
  quality?: string
  fps?: number
  qualityLabel?: string
  projectionType?: string
  averageBitrate?: number
  audioQuality?: string
  approxDurationMs?: string
  audioSampleRate?: string
  audioChannels?: number
  qualityOrdinal?: string
  url: string
}

type VideoThumbnail = {
  url: string
  width: number
  height: number
}

type Storyboard = {
  url: string[]
  width: string | number
  height: string | number
  thumbsCount: string | number
  columns: number
  rows: number
  interval?: number
  storyboardCount?: number
}

type VideoInfo = {
  data: {
    id: string
    title: string
    lengthSeconds: string
    keywords: string[]
    channelTitle: string
    channelId: string
    description: string
    thumbnail: VideoThumbnail[]
    allowRatings: boolean
    viewCount: string
    isPrivate: boolean
    isUnlisted: boolean
    isFamilySafe: boolean
    availableCountries: string[]
    category: string
    publishDate?: string
    uploadDate?: string
    storyboards?: Storyboard[]
    // Add other fields as needed
  }
  apiRemain: number
}

type VideoData = {
  data: {
    formats: VideoFormat[]
  }
  apiRemain: number
}

export type MappedVideo = {
  type: string
  title: string
  videoId: string
  videoThumbnails: Array<{
    quality: string
    url: string
    width: number
    height: number
  }>
  storyboards: Array<{
    url: string
    templateUrl: string
    width: number
    height: number
    count: number
    interval: number
    storyboardWidth: number
    storyboardHeight: number
    storyboardCount: number
  }>
  description: string
  descriptionHtml: string
  published: number
  publishedText: string
  keywords: string[]
  viewCount: number
  likeCount: number
  dislikeCount: number
  paid: boolean
  premium: boolean
  isFamilyFriendly: boolean
  allowedRegions: string[]
  genre: string
  genreUrl: string | null
  author: string
  authorId: string
  authorUrl: string
  authorVerified: boolean
  authorThumbnails: Array<{
    url: string
    width: number
    height: number
  }>
  subCountText: string
  lengthSeconds: number
  allowRatings: boolean
  rating: number
  isListed: boolean
  liveNow: boolean
  isUpcoming: boolean
  dashUrl: string
  formatStreams: Array<{
    url: string
    itag: string
    type: string
    quality: string
    bitrate?: string
    fps: number
    size: string
    resolution: string
    qualityLabel: string
    container: string
    encoding: string
  }>
  adaptiveFormats: Array<{
    init: string
    index: string
    bitrate: string
    url: string
    itag: string
    type: string
    clen: string
    lmt: string
    projectionType: string
    fps?: number
    container: string
    encoding: string
    qualityLabel?: string
    resolution: string
    audioQuality?: string
    audioSampleRate?: string
    audioChannels?: number
  }>
  captions: Array<{
    label: string
    languageCode: string
    url: string
  }>
  recommendedVideos: Array<{
    videoId: string
    title: string
    videoThumbnails: Array<{
      quality: string
      url: string
      width: number
      height: number
    }>
    author: string
    authorUrl: string
    authorId: string
    lengthSeconds: number
    viewCountText: string
    viewCount: number
  }>
}

export function mapVideoData(videoData: VideoData, videoInfo: VideoInfo): MappedVideo {
  const getTimeAgo = (dateString: string): string => {
    const date = new Date(dateString)
    const seconds = Math.floor((Date.now() - date.getTime()) / 1000)

    const intervals = {
      year: 31536000,
      month: 2592000,
      week: 604800,
      day: 86400,
      hour: 3600,
      minute: 60,
    }

    for (const [unit, secondsInUnit] of Object.entries(intervals)) {
      const interval = Math.floor(seconds / secondsInUnit)
      if (interval >= 1) {
        return interval === 1 ? `1 ${unit} ago` : `${interval} ${unit}s ago`
      }
    }
    return 'Just now'
  }

  const publishedDate =
    videoInfo.data.publishDate || videoInfo.data.uploadDate || new Date().toISOString()
  const publishedTimestamp = Math.floor(new Date(publishedDate).getTime() / 1000)

  return {
    type: 'video',
    title: videoInfo.data.title,
    videoId: videoInfo.data.id,
    videoThumbnails:
      videoInfo.data.thumbnail?.map((thumb) => {
        let quality = 'default'
        if (thumb.width === 320 && thumb.height === 180) quality = 'medium'
        else if (thumb.width === 480 && thumb.height === 360) quality = 'high'
        else if (thumb.width === 640 && thumb.height === 480) quality = 'standard'
        else if (thumb.width === 1280 && thumb.height === 720) quality = 'maxres'

        return {
          quality,
          url: thumb.url,
          width: thumb.width,
          height: thumb.height,
        }
      }) || [],
    storyboards:
      videoInfo.data.storyboards?.map((sb) => ({
        url: `/api/v1/storyboards/${videoInfo.data.id}?width=${sb.width}&height=${sb.height}`,
        templateUrl: Array.isArray(sb.url) ? sb.url[0]?.split('?')[0] || '' : '',
        width: typeof sb.width === 'string' ? parseInt(sb.width) : sb.width || 0,
        height: typeof sb.height === 'string' ? parseInt(sb.height) : sb.height || 0,
        count: typeof sb.thumbsCount === 'string' ? parseInt(sb.thumbsCount) : sb.thumbsCount || 0,
        interval: sb.interval || 0,
        storyboardWidth: sb.columns || 0,
        storyboardHeight: sb.rows || 0,
        storyboardCount: typeof sb.storyboardCount === 'number' ? sb.storyboardCount : 1,
      })) || [],
    description: videoInfo.data.description,
    descriptionHtml: videoInfo.data.description.replace(/\n/g, '<br>'),
    published: publishedTimestamp,
    publishedText: getTimeAgo(publishedDate),
    keywords: videoInfo.data.keywords || [],
    viewCount: parseInt(videoInfo.data.viewCount) || 0,
    likeCount: 0,
    dislikeCount: 0,
    paid: false,
    premium: false,
    isFamilyFriendly: videoInfo.data.isFamilySafe,
    allowedRegions: videoInfo.data.availableCountries || [],
    genre: videoInfo.data.category || 'Unknown',
    genreUrl: null,
    author: videoInfo.data.channelTitle,
    authorId: videoInfo.data.channelId,
    authorUrl: `/channel/${videoInfo.data.channelId}`,
    authorVerified: true,
    authorThumbnails: [],
    subCountText: '',
    lengthSeconds: parseInt(videoInfo.data.lengthSeconds) || 0,
    allowRatings: videoInfo.data.allowRatings,
    rating: 0,
    isListed: !videoInfo.data.isUnlisted,
    liveNow: false,
    isUpcoming: false,
    dashUrl: '',
    formatStreams:
      videoData.data.formats?.map((format) => {
        let container = 'mp4'
        if (format.mimeType?.includes('webm')) container = 'webm'

        let encoding = 'h264'
        if (format.mimeType?.includes('vp9')) encoding = 'vp9'
        else if (format.mimeType?.includes('av01')) encoding = 'av01'

        const qualityLabels: Record<string, string> = {
          tiny: '144p',
          small: '240p',
          medium: '360p',
          large: '480p',
          hd720: '720p',
          hd1080: '1080p',
          hd1440: '1440p',
          hd2160: '2160p',
        }

        const quality = format.quality || ''
        const qualityLabel = format.qualityLabel || qualityLabels[quality] || ''
        const resolution = format.height ? `${format.height}p` : ''
        const size = format.width && format.height ? `${format.width}x${format.height}` : ''

        return {
          url: format.url,
          itag: format.itag?.toString() || '',
          type: format.mimeType || '',
          quality,
          bitrate: format.bitrate?.toString(),
          fps: format.fps || 30,
          size,
          resolution,
          qualityLabel,
          container,
          encoding,
        }
      }) || [],
    adaptiveFormats:
      videoData.data.formats?.map((format) => {
        let container = 'mp4'
        if (format.mimeType?.includes('webm')) container = 'webm'

        let encoding = 'h264'
        if (format.mimeType?.includes('vp9')) encoding = 'vp9'
        else if (format.mimeType?.includes('av01')) encoding = 'av01'

        return {
          init: '0-0',
          index: '0-0',
          bitrate: format.bitrate?.toString() || '0',
          url: format.url,
          itag: format.itag?.toString() || '',
          type: format.mimeType || '',
          clen: format.contentLength || '0',
          lmt: format.lastModified || '0',
          projectionType: format.projectionType || 'RECTANGULAR',
          fps: format.fps,
          container,
          encoding,
          qualityLabel: format.qualityLabel || '',
          resolution: format.width && format.height ? `${format.width}x${format.height}` : '',
          audioQuality: format.audioQuality || '',
          audioSampleRate: format.audioSampleRate || '',
          audioChannels: format.audioChannels,
        }
      }) || [],
    captions: [],
    recommendedVideos: [],
  }
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function mapCompactVideo(data: any) {
  if (data.type !== 'CompactVideo') return null

  const videoId = data.video_id
  const title = data.title?.text || ''
  const author = data.author?.name || ''
  const authorId = data.author?.id || ''
  const authorUrl = data.author?.url || ''
  const authorVerified =
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    data.author?.badges?.some((b: any) => b.icon === 'CHECK_CIRCLE') || false

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const videoThumbnails = (data.thumbnails || []).map((t: any) => ({
    url: t.url,
    width: t.width,
    height: t.height,
  }))

  const viewCountText = data.view_count?.text || ''
  const viewCount = parseInt(viewCountText.replace(/[^\d]/g, '')) || 0
  const publishedText = data.published?.text || ''
  const lengthText = data.length_text?.text || ''

  return {
    type: 'video',
    title,
    videoId,
    author,
    authorId,
    authorUrl,
    authorVerified,
    videoThumbnails,
    viewCount,
    viewCountText,
    published: parseRelativeTimeToTimestamp(publishedText),
    publishedText,
    lengthSeconds: parseDuration(lengthText),
  }
}
