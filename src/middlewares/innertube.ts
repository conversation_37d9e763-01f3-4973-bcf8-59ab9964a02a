import { createMiddleware } from 'hono/factory'

import Innertube, { Log } from 'youtubei.js/cf-worker'

export const innertube = () => {
  return createMiddleware(async (c, next) => {
    const region = c.req.query('region')
    const yt = await Innertube.create({
      generate_session_locally: true,
      retrieve_player: false,
      location: region,
      lang: region === 'VN' ? 'vi' : 'en',
    })

    Log.setLevel(Log.Level.ERROR)
    c.set('yt', yt)
    await next()
  })
}
