export type Range = {
  start: string
  end: string
}

export type Thumbnail = {
  url: string
  width: number
  height: number
}

export type Format = {
  itag: number
  mimeType: string
  bitrate: number
  width?: number
  height?: number
  initRange?: Range
  indexRange?: Range
  lastModified: string
  contentLength: string
  quality: string
  fps?: number
  qualityLabel?: string
  averageBitrate: number
  approxDurationMs: string
  qualityOrdinal: string
  url: string
  highReplication?: boolean
  audioQuality?: string
  audioSampleRate?: string
  audioChannels?: number
  loudnessDb?: number
}

export type YoutubeVideo = {
  status: string
  id: string
  title: string
  lengthSeconds: string
  keywords: string[]
  channelTitle: string
  channelId: string
  description: string
  thumbnail: Thumbnail[]
  allowRatings: boolean
  viewCount: string
  isPrivate: boolean
  isUnpluggedCorpus: boolean
  isLiveContent: boolean
  isFamilySafe: boolean
  availableCountries: string[]
  isUnlisted: boolean
  category: string
  publishDate: Date
  uploadDate: Date
  isShortsEligible: boolean
  expiresInSeconds: string
  formats: Format[]
  isGCR: boolean
  adaptiveFormats: Format[]
  pmReg: string
  isProxied: boolean
}

export type VideoResponse = {
  data: YoutubeVideo
  apiRemain: number
}
