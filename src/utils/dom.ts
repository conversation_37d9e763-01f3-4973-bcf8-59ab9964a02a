import render from 'dom-serializer'
import { type ChildNode, type Element, Text } from 'domhandler'
import { parseDocument } from 'htmlparser2'

import { videoConverterUrl, webBaseUrl } from '.'

const UNWANTED_KEYWORDS = [
  'fixed-start',
  'fixed-header',
  'siterubric',
  'navtab',
  'knp_all',
  'hr',
  'mbob',
  'sharethis',
] as const

const ATTRIBUTES_TO_PROCESS = ['href', 'src', 'action', 'data', 'formaction'] as const

function shouldRemoveElement(element: Element): boolean {
  // Block meta refresh
  if (element.tagName === 'meta' && element.attribs?.['http-equiv']?.toLowerCase() === 'refresh') {
    return true
  }

  const id = element.attribs?.id || ''
  const className = element.attribs?.class || ''

  return UNWANTED_KEYWORDS.some(
    (keyword) => id.includes(keyword) || className.includes(keyword) || className === 'right15',
  )
}

function isStaticAsset(element: Element, attr: string, url: string): boolean {
  if (
    /\.(mp4|webm|ogg|avi|mov|mp3|wav|flac|aac)$/i.test(url) ||
    (attr === 'src' && ['video', 'audio'].includes(element.tagName))
  ) {
    return false
  }

  if (attr === 'src' || attr === 'data') {
    return true
  }

  if (attr === 'href') {
    return (
      /\.(css|js|ico|png|jpg|jpeg|gif|svg)$/i.test(url) ||
      (element.tagName === 'link' &&
        (element.attribs?.rel === 'stylesheet' || element.attribs?.rel === 'icon'))
    )
  }

  return false
}

function processUrl(element: Element, attr: string, url: string, proxyBase: string): string {
  if (url.startsWith('//') || /^rtsp:\/\//i.test(url)) {
    return url
  }

  if (url.startsWith('http')) {
    return `/relay?u=${encodeURIComponent(url)}`
  }

  const fullUrl = url.startsWith('/') ? url : `/${url}`

  if (isStaticAsset(element, attr, url)) {
    return `${videoConverterUrl}${fullUrl}`
  }

  return `${proxyBase}${encodeURIComponent(fullUrl)}`
}

function processElement(element: Element, proxyBase: string): void {
  if (shouldRemoveElement(element)) {
    element.parent?.children?.splice(element.parent.children.indexOf(element), 1)
    return
  }

  for (const attr of ATTRIBUTES_TO_PROCESS) {
    if (element.attribs?.[attr]) {
      element.attribs[attr] = processUrl(element, attr, element.attribs[attr], proxyBase)
    }
  }

  if (element.tagName === 'input' && element.attribs?.name === 'ln' && element.attribs?.value) {
    const { value } = element.attribs
    element.attribs.value = value.includes('video.2yxa.mobi')
      ? `${webBaseUrl}/relay?u=${encodeURIComponent(value)}`
      : `${proxyBase}${value.startsWith('/') ? '' : '/'}${encodeURIComponent(value)}`
  }

  if (element.attribs?.onclick) {
    element.attribs.onclick = element.attribs.onclick.replace(
      /preview\('([^']*)','([^']*)','([^']*)'\)/g,
      (match, id, videoUrl, imageUrl) => {
        const fullVideoUrl = videoUrl.startsWith('/')
          ? `${videoConverterUrl}${videoUrl}`
          : `${videoConverterUrl}/${videoUrl}`

        const fullImageUrl = imageUrl.startsWith('/')
          ? `${videoConverterUrl}${imageUrl}`
          : `${videoConverterUrl}/${imageUrl}`

        return `preview('${id}','${fullVideoUrl}','${fullImageUrl}')`
      },
    )
  }

  if (element.children) {
    element.children.forEach((child) => {
      if (child.type === 'tag') {
        processElement(child, proxyBase)
      }
    })
  }
}

function modifyElementText(element: Element, newText: string): void {
  if (element.type === 'tag') {
    const classNames = element.attribs?.class?.split(' ') || []
    if (classNames.includes('sm') && classNames.includes('fio') && classNames.includes('left15')) {
      // Remove all children and set new text content
      const textNode = new Text(newText)
      textNode.parent = element
      element.children = [textNode as ChildNode]
    }

    // Process children recursively
    if (element.children) {
      element.children.forEach((child) => {
        if (child.type === 'tag') {
          modifyElementText(child, newText)
        }
      })
    }
  }
}

export function replaceRelativeHref(
  html: string,
  proxyBase: string,
  options: { smFioLeft15Text?: string } = {},
): string {
  html = html
    .replace(/<script[^>]+sdk\.adlook\.tech[^<]*<\/script>/g, '')
    .replace(/<script[^>]*src=["']\/flash\/js\/PreviewVideo\.js\?[^"']*["'][^>]*><\/script>/gi, '')

  const dom = parseDocument(html)

  // Process all elements for URL rewriting
  dom.children.forEach((child) => {
    if (child.type === 'tag') {
      processElement(child, proxyBase)

      // Modify .sm.fio.left15 elements if text is provided
      if (options.smFioLeft15Text) {
        modifyElementText(child, options.smFioLeft15Text)
      }
    }
  })

  return render(dom)
}

export function decodeHtmlEntities(str: string) {
  return str
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")
    .replace(/&#(\d+);/g, (_, num) => String.fromCharCode(num))
    .replace(/&#x([0-9a-f]+);/gi, (_, hex) => String.fromCharCode(parseInt(hex, 16)))
}
