import { HTTPException } from 'hono/http-exception'

import { API_MIN_THRESHOLD } from '.'

import type * as schema from '../db/schema'
import type { DrizzleD1Database } from 'drizzle-orm/d1'

type AppDatabase = DrizzleD1Database<typeof schema>

type FetchWithRetryOptions<T> = {
  drizzle: AppDatabase
  fetchFn: (apiKey: string, useApi1: boolean) => Promise<T & { apiRemain?: number }>
  updateRemainingFn: (
    drizzle: AppDatabase,
    accountId: number,
    isApi1: boolean,
    remain: number,
  ) => Promise<void>
  getAccountFn: (drizzle: AppDatabase) => Promise<{
    id: number
    apiKey: string
    api1Remain?: number | null
  }>
}

export async function fetchWithRetry<T>({
  drizzle,
  fetchFn,
  updateRemainingFn,
  getAccountFn,
}: FetchWithRetryOptions<T>): Promise<{
  data: T
  accountId: number
  currentAccount: { id: number; apiKey: string; api1Remain?: number | null }
}> {
  const MAX_RETRIES = 5
  let retryCount = 0
  let lastError: Error | null = null

  const currentAccount = await getAccountFn(drizzle)
  let currentAccountId = currentAccount.id
  let currentApiKey = currentAccount.apiKey
  let currentUseApi1 = (currentAccount.api1Remain ?? 0) > API_MIN_THRESHOLD

  while (retryCount < MAX_RETRIES) {
    try {
      const data = await fetchFn(currentApiKey, currentUseApi1)
      await updateRemainingFn(drizzle, currentAccountId, currentUseApi1, data.apiRemain ?? -1)
      return { data, accountId: currentAccountId, currentAccount }
    } catch (error) {
      const httpError = error as HTTPException
      lastError = httpError

      if ([429, 401, 403].includes(httpError.status)) {
        updateRemainingFn(drizzle, currentAccountId, currentUseApi1, 0)

        const newAccount = await getAccountFn(drizzle)
        if (!newAccount) {
          break
        }

        currentAccountId = newAccount.id
        currentApiKey = newAccount.apiKey
        currentUseApi1 = (newAccount.api1Remain ?? 0) > API_MIN_THRESHOLD
        retryCount++
      } else if ([502, 503, 504].includes(httpError.status)) {
        currentUseApi1 = !currentUseApi1
        retryCount++
      } else {
        throw httpError
      }
    }
  }

  throw new HTTPException(503, {
    message: `Failed after ${retryCount} attempts. Last error: ${lastError?.message || 'Unknown error'}`,
  })
}
