export function getIndexByTime(totalItems = 1544): number {
  const now = new Date()
  const msSinceMidnight =
    now.getHours() * 3600000 +
    now.getMinutes() * 60000 +
    now.getSeconds() * 1000 +
    now.getMilliseconds()

  const interval = 86400000 / totalItems
  return Math.floor(msSinceMidnight / interval)
}

export function parseRelativeTimeToTimestamp(text: string): number | null {
  const normalizedText = text.trim().toLowerCase()

  const englishMatch = normalizedText.match(
    /^(\d+)\s+(second|minute|hour|day|week|month|year)s?\s+ago$/i,
  )

  const vietnamesePatterns = /^(\d+)\s+(giây|phút|giờ|ngày|tuần|tháng|năm)\s+trước$/

  let value: number
  let unit: string

  if (englishMatch) {
    value = parseInt(englishMatch[1], 10)
    unit = englishMatch[2].toLowerCase()
  } else {
    let vietnameseMatch: RegExpMatchArray | null = null
    vietnameseMatch = normalizedText.match(vietnamesePatterns)

    if (!vietnameseMatch) return null

    value = parseInt(vietnameseMatch[1], 10)
    const vietnameseUnit = vietnameseMatch[2]

    const unitMapping: { [key: string]: string } = {
      giây: 'second',
      phút: 'minute',
      giờ: 'hour',
      ngày: 'day',
      tuần: 'week',
      tháng: 'month',
      năm: 'year',
    }

    unit = unitMapping[vietnameseUnit]
    if (!unit) return null
  }

  const now = new Date()
  const targetDate = new Date(now)

  switch (unit) {
    case 'second':
      targetDate.setSeconds(targetDate.getSeconds() - value)
      break
    case 'minute':
      targetDate.setMinutes(targetDate.getMinutes() - value)
      break
    case 'hour':
      targetDate.setHours(targetDate.getHours() - value)
      break
    case 'day':
      targetDate.setDate(targetDate.getDate() - value)
      break
    case 'week':
      targetDate.setDate(targetDate.getDate() - value * 7)
      break
    case 'month':
      targetDate.setMonth(targetDate.getMonth() - value)
      break
    case 'year':
      targetDate.setFullYear(targetDate.getFullYear() - value)
      break
    default:
      return null
  }

  return Math.floor(targetDate.getTime() / 1000)
}

export function timestampToRelativeTimePrecise(timestamp: string | number | Date): string {
  try {
    const pastDate = new Date(timestamp)
    const now = new Date()

    if (isNaN(pastDate.getTime())) {
      return 'Invalid date'
    }

    if (pastDate > now) {
      return 'In the future'
    }

    let years = now.getFullYear() - pastDate.getFullYear()
    let months = now.getMonth() - pastDate.getMonth()
    let days = now.getDate() - pastDate.getDate()

    if (days < 0) {
      months--
      const prevMonth = new Date(now.getFullYear(), now.getMonth(), 0)
      days += prevMonth.getDate()
    }

    if (months < 0) {
      years--
      months += 12
    }

    if (years === 0 && months === 0) {
      const diffMs = now.getTime() - pastDate.getTime()
      const diffSeconds = Math.floor(diffMs / 1000)
      const diffMinutes = Math.floor(diffSeconds / 60)
      const diffHours = Math.floor(diffMinutes / 60)
      const diffDays = Math.floor(diffHours / 24)
      const diffWeeks = Math.floor(diffDays / 7)

      if (diffWeeks > 0) {
        return `${diffWeeks} week${diffWeeks > 1 ? 's' : ''} ago`
      } else if (diffDays > 0) {
        return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`
      } else if (diffHours > 0) {
        return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`
      } else if (diffMinutes > 0) {
        return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`
      } else {
        return `${diffSeconds} second${diffSeconds > 1 ? 's' : ''} ago`
      }
    }

    if (years > 0) {
      return `${years} year${years > 1 ? 's' : ''} ago`
    } else {
      return `${months} month${months > 1 ? 's' : ''} ago`
    }
  } catch {
    return 'Invalid date'
  }
}

export function random6Digit(): string {
  return Math.floor(100000 + Math.random() * 900000).toString()
}

export function parseCount(text?: string): number {
  try {
    if (!text) return 0

    const normalizedText = text.toLowerCase().trim()

    const englishMatch = normalizedText.match(/([\d,.]+)\s*([kmb])(?![a-z])/)
    if (englishMatch) {
      const num = parseFloat(englishMatch[1].replace(/,/g, ''))
      const unit = englishMatch[2]

      if (isNaN(num)) return 0

      switch (unit) {
        case 'k':
          return Math.round(num * 1e3)
        case 'm':
          return Math.round(num * 1e6)
        case 'b':
          return Math.round(num * 1e9)
        default:
          return Math.round(num)
      }
    }

    let vietnameseMatch = null

    if (!vietnameseMatch) {
      vietnameseMatch = normalizedText.match(/([\d,.]+)\s*(tr)(?=\s|$|[^a-z])/)
    }
    if (!vietnameseMatch) {
      vietnameseMatch = normalizedText.match(/([\d,.]+)\s*(n)(?=\s|$|[^a-z])/)
    }
    if (!vietnameseMatch) {
      vietnameseMatch = normalizedText.match(/([\d,.]+)\s*(t)(?=\s|$|[^a-z])/)
    }
    if (!vietnameseMatch) {
      vietnameseMatch = normalizedText.match(/([\d,.]+)/)
    }

    if (vietnameseMatch) {
      let numStr = vietnameseMatch[1]
      const unit = vietnameseMatch[2] || null

      if (numStr.includes('.') && numStr.includes(',')) {
        const lastDot = numStr.lastIndexOf('.')
        const lastComma = numStr.lastIndexOf(',')

        if (lastComma > lastDot) {
          numStr = numStr.replace(/\./g, '').replace(',', '.')
        } else {
          numStr = numStr.replace(/,/g, '')
        }
      } else if (numStr.includes('.')) {
        if (numStr.split('.').length > 2 || (numStr.split('.').length === 2 && !unit)) {
          numStr = numStr.replace(/\./g, '')
        }
      } else if (numStr.includes(',')) {
        if (numStr.split(',').length > 2) {
          numStr = numStr.replace(/,/g, '')
        } else if (unit) {
          numStr = numStr.replace(',', '.')
        } else {
          numStr = numStr.replace(/,/g, '')
        }
      }

      const num = parseFloat(numStr)
      if (isNaN(num)) return 0

      switch (unit) {
        case 'n':
          return Math.round(num * 1e3)
        case 'tr':
          return Math.round(num * 1e6)
        case 't':
          return Math.round(num * 1e9)
        default:
          return Math.round(num)
      }
    }

    return 0
  } catch {
    return 0
  }
}

export function parseDuration(text?: string): number {
  if (!text) return 0

  const parts = text.split(':').reverse()
  return parts.reduce((acc, val, i) => acc + (parseInt(val) || 0) * Math.pow(60, i), 0)
}

export function abbreviateTimeUnits(text: string): string {
  if (typeof text !== 'string') {
    return text
  }

  const unitAbbreviations: Record<string, string> = {
    seconds: 's',
    second: 's',
    minutes: 'min',
    minute: 'min',
    hours: 'h',
    hour: 'h',
    days: 'd',
    day: 'd',
    weeks: 'w',
    week: 'w',
    months: 'mo',
    month: 'mo',
    years: 'yrs',
    year: 'yr',
  }

  const sortedUnits = Object.keys(unitAbbreviations).sort((a, b) => b.length - a.length)
  const regex = new RegExp(`(\\d+)(\\s+)(${sortedUnits.join('|')})\\b`, 'gi')

  return text.replace(regex, (match, number, space, unit: keyof typeof unitAbbreviations) => {
    const abbreviation = unitAbbreviations[unit.toLowerCase()] || unit
    return `${number} ${abbreviation}`
  })
}
