export type PaginationParams = {
  page: number
  itemsPerPage: number
}

export type PaginationResult<T> = {
  data: T[]
  pagination: {
    currentPage: number
    totalItems: number
    itemsPerPage: number
    baseUrl: string
  }
}

export function paginateData<T>(
  data: T[],
  params: PaginationParams,
  baseUrl: string,
): PaginationResult<T> {
  const { page, itemsPerPage } = params
  const startIndex = (page - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage

  return {
    data: data.slice(startIndex, endIndex),
    pagination: {
      currentPage: page,
      totalItems: data.length,
      itemsPerPage,
      baseUrl,
    },
  }
}
