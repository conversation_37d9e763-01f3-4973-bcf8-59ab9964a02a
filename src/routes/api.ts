import { HTTPException } from 'hono/http-exception'

import { Hono } from 'hono'
import { YTNodes } from 'youtubei.js/cf-worker'

import { mapChannelResponse, mapChannelVideo } from '../mappers/channel.mapper'
import { mapPlaylists, mapPlaylistVideo } from '../mappers/playlist.mapper'
import { type DataSearch, mapDataSearchToNewFormat } from '../mappers/search.mapper'
import { type DataTrending, mapDataTrendingToNewFormat } from '../mappers/trending.mapper'
import { mapCompactVideo } from '../mappers/video.mapper'
import { cgeo, drizzle, innertube } from '../middlewares'
import { AccountService } from '../services/account'
import { ApiService } from '../services/api'
import { PlayableLinkService } from '../services/playable-link'
import {
  CONSTANTS,
  fetchWithRetry,
  pageCache,
  parseCount,
  parseDuration,
  parseRelativeTimeToTimestamp,
} from '../utils'

import type { VideoResponse } from '../types'

const router = new Hono<AppEnv>()

router.get('/search', innertube(), async (c) => {
  const q = c.req.query('q')
  if (!q) throw new HTTPException(400, { message: 'Missing query parameter' })
  const page = Number(c.req.query('page') || '1')

  // Optimize cache key generation
  const cacheKey = `api:${q}:${page - 1}`
  const currentPageKey = `api:${q}:${page}`

  try {
    const cachedResult = pageCache.get(cacheKey)
    const result = cachedResult?.has_continuation
      ? await cachedResult.getContinuation()
      : await c.get('yt').search(q, { sort_by: 'relevance' })

    // Only cache if result is valid
    if (result) {
      pageCache.set(currentPageKey, result)
    }

    return c.json(mapDataSearchToNewFormat(result as unknown as DataSearch))
  } catch {
    return c.json([])
  }
})

router.get('/search/suggestions', innertube(), async (c) => {
  const q = c.req.query('q')
  if (!q) throw new HTTPException(400, { message: 'Missing query parameter' })

  try {
    const suggestions = await c.get('yt').getSearchSuggestions(q)
    return c.json({ query: q, suggestions })
  } catch {
    return c.json({ query: q, suggestions: [] })
  }
})

router.get('/trending', innertube(), async (c) => {
  try {
    const trending = await c.get('yt').getTrending()
    const video = trending.videos as DataTrending[]

    return c.json(video.map(mapDataTrendingToNewFormat).filter(Boolean))
  } catch {
    return c.json([])
  }
})

router.get('/popular', innertube(), async (c) => {
  try {
    const trending = await c.get('yt').getTrending()
    const video = trending.videos as DataTrending[]

    return c.json(video.map(mapDataTrendingToNewFormat).filter(Boolean))
  } catch {
    return c.json([])
  }
})

router.get('/channels/:id', innertube(), async (c) => {
  const id = c.req.param('id')
  if (!id) throw new HTTPException(400, { message: 'Missing channel ID' })

  try {
    const channel = await c.get('yt').getChannel(id)
    return c.json(mapChannelResponse(channel))
  } catch {
    return c.json({ error: 'Failed to fetch channel' }, 500)
  }
})

router.get('/channels/:id/videos', innertube(), async (c) => {
  const id = c.req.param('id')
  if (!id) throw new HTTPException(400, { message: 'Missing channel ID' })

  try {
    const channel = await c.get('yt').getChannel(id)

    return c.json({
      videos: channel.videos
        .map((video) =>
          mapChannelVideo(video, {
            title: channel.metadata.title || '',
            external_id: channel.metadata.external_id || '',
            url: channel.metadata.url || '',
          }),
        )
        .filter(Boolean),
    })
  } catch {
    return c.json({ videos: [] })
  }
})

router.get('/channels/:id/latest', innertube(), async (c) => {
  const id = c.req.param('id')
  if (!id) throw new HTTPException(400, { message: 'Missing channel ID' })

  try {
    const channel = await c.get('yt').getChannel(id)

    return c.json({
      videos: channel.videos
        .map((video) =>
          mapChannelVideo(video, {
            title: channel.metadata.title || '',
            external_id: channel.metadata.external_id || '',
            url: channel.metadata.url || '',
          }),
        )
        .filter(Boolean),
    })
  } catch {
    return c.json({ videos: [] })
  }
})

router.get('/channels/search/:id', innertube(), async (c) => {
  const id = c.req.param('id')
  const q = c.req.query('q')

  if (!id || !q) throw new HTTPException(400, { message: 'Missing channel ID or query' })

  try {
    const channel = await c.get('yt').getChannel(id)
    const search = await channel.search(q)

    return c.json(
      search.videos
        .map((video) => {
          if (!video.is(YTNodes.Video)) return null

          return {
            type: video.type.toLowerCase(),
            title: video.title?.text || '',
            videoId: video.video_id || '',
            author: video.author?.name || '',
            authorId: video.author?.id || '',
            authorUrl: `/channel/${video.author?.id}`,
            authorVerified: true,
            videoThumbnails: video.thumbnails || [],
            description: video.description,
            descriptionHtml: video.description_snippet?.text || '',
            viewCount: parseCount(video.view_count?.text),
            viewCountText: video.view_count?.text || '',
            published: parseRelativeTimeToTimestamp(video?.published?.text || '') || 0,
            publishedText: video.published?.text || '',
            lengthSeconds: video.duration?.text ? parseDuration(video.duration.text) : 0,
          }
        })
        .filter(Boolean),
    )
  } catch {
    return c.json([])
  }
})

router.get('/channels/:id/search', innertube(), async (c) => {
  const id = c.req.param('id')
  const q = c.req.query('q')

  if (!id || !q) throw new HTTPException(400, { message: 'Missing channel ID or query' })

  try {
    const channel = await c.get('yt').getChannel(id)
    const search = await channel.search(q)

    return c.json(
      search.videos
        .map((video) => {
          if (!video.is(YTNodes.Video)) return null

          return {
            type: video.type.toLowerCase(),
            title: video.title?.text || '',
            videoId: video.video_id || '',
            author: video.author?.name || '',
            authorId: video.author?.id || '',
            authorUrl: `/channel/${video.author?.id}`,
            authorVerified: true,
            videoThumbnails: video.thumbnails || [],
            description: video.description,
            descriptionHtml: video.description_snippet?.text || '',
            viewCount: parseCount(video.view_count?.text),
            viewCountText: video.view_count?.text || '',
            published: parseRelativeTimeToTimestamp(video?.published?.text || '') || 0,
            publishedText: video.published?.text || '',
            lengthSeconds: video.duration?.text ? parseDuration(video.duration.text) : 0,
          }
        })
        .filter(Boolean),
    )
  } catch {
    return c.json([])
  }
})

router.get('/channels/playlists/:id', innertube(), async (c) => {
  const id = c.req.param('id')
  if (!id) throw new HTTPException(400, { message: 'Missing channel ID' })

  try {
    const channel = await c.get('yt').getChannel(id)
    const playlists = await channel.getPlaylists()
    const content = playlists.current_tab?.content

    const sectionList = content?.is(YTNodes.SectionList) ? content.contents[0] : null

    return c.json(mapPlaylists(sectionList, channel))
  } catch {
    return c.json({ playlists: [] })
  }
})

router.get('/channels/:id/playlists', innertube(), async (c) => {
  const id = c.req.param('id')
  if (!id) throw new HTTPException(400, { message: 'Missing channel ID' })

  try {
    const channel = await c.get('yt').getChannel(id)
    const playlists = await channel.getPlaylists()
    const content = playlists.current_tab?.content

    const sectionList = content?.is(YTNodes.SectionList) ? content.contents[0] : null

    return c.json(mapPlaylists(sectionList, channel))
  } catch {
    return c.json({ playlists: [] })
  }
})

router.get('/playlists/:playlistId', innertube(), async (c) => {
  const playlistId = c.req.param('playlistId')
  if (!playlistId) throw new HTTPException(400, { message: 'Missing playlist ID' })

  try {
    const playlist = await c.get('yt').getPlaylist(playlistId)

    return c.json({
      type: 'playlist',
      title: playlist.info.title,
      playlistId,
      playlistThumbnail: playlist.info.thumbnails[0].url,
      author: playlist.info.author.name,
      authorId: playlist.info.author.id,
      authorUrl: `/channel/${playlist.info.author.id}`,
      subtitle: null,
      authorThumbnails: [],
      description: playlist.info.description,
      descriptionHtml: playlist.info.description,
      videoCount: parseCount(playlist.info.total_items),
      viewCount: parseCount(playlist.info.views),
      videos: playlist.videos.map((video) => mapPlaylistVideo(video, playlist)).filter(Boolean),
    })
  } catch {
    return c.json({ videos: [] })
  }
})

router.get('/videos/:id', cgeo(), drizzle(), innertube(), async (c) => {
  const id = c.req.param('id')
  if (!id) throw new HTTPException(400, { message: 'Missing video ID' })

  try {
    const [{ data: videoData, accountId: currentAccountId }, videoInfo] = await Promise.all([
      fetchWithRetry<VideoResponse>({
        drizzle: c.get('drizzle'),

        fetchFn: async (apiKey, useApi1) => {
          const result = await ApiService.fetchVideoData(id, apiKey, useApi1, c.get('cgeo'))
          return result
        },

        updateRemainingFn: async (db, accId, isApi1, remain) => {
          await AccountService.updateApiRemaining(
            db,
            accId,
            isApi1 ? { api1Remain: remain } : { api2Remain: remain },
          )
        },

        getAccountFn: async (db) => {
          const acc = await AccountService.getAvailableAccount(db)
          return {
            id: acc.id,
            apiKey: acc.apiKey,
            api1Remain: acc.api1Remain,
          }
        },
      }),

      c.get('yt').getInfo(id),
    ])

    c.executionCtx.waitUntil(AccountService.updateLastUsed(c.get('drizzle'), currentAccountId))

    const format = videoData.data.formats.find((f) => f.itag === CONSTANTS.VIDEO_ITAG)

    if (!format?.url) {
      throw new HTTPException(404, { message: 'Video format not found' })
    }

    c.executionCtx.waitUntil(
      PlayableLinkService.upsertPlayableLink(c.get('drizzle'), id, format.url, 'video'),
    )

    return c.json({
      type: 'video',
      title: videoData.data.title,
      videoId: videoData.data.id,
      videoThumbnails: videoData.data.thumbnail,
      description: videoData.data.description,
      published: Math.floor(
        new Date(videoInfo.primary_info?.published?.text || '').getTime() / 1000,
      ),
      publishedText: videoInfo.primary_info?.relative_date.text,
      keywords: videoData.data.keywords,
      viewCount: Number(videoData.data.viewCount),
      likeCount: Number(videoInfo.basic_info.like_count),
      author: videoData.data.channelTitle,
      authorId: videoData.data.channelId,
      authorUrl: `/channel/${videoData.data.channelId}`,
      authorThumbnails: videoInfo.secondary_info?.owner?.author?.thumbnails,
      subCountText: videoInfo.secondary_info?.owner?.subscriber_count.text,
      lengthSeconds: Number(videoData.data.lengthSeconds),
      formatStreams: videoData.data.formats.map((f) => ({
        ...f,
        url: id,
      })),
      recommendedVideos: videoInfo.watch_next_feed?.map(mapCompactVideo).filter(Boolean),
    })
  } catch {
    return c.json({ error: 'Failed to fetch video' }, 500)
  }
})

export default router
