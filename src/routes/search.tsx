import { HTTPException } from 'hono/http-exception'

import { Hono } from 'hono'

import MainLayout from '../../views/MainLayout'
import SearchPage from '../../views/Search'
import { cgeo, innertube } from '../middlewares'
import { filterData, pageCache } from '../utils'

import type { SearchReturn } from '../types'

const router = new Hono<AppEnv>()

router.get('/search', cgeo(), innertube(), MainLayout('S60Tube - Search Results'), async (c) => {
  const q = c.req.query('q')
  if (!q) return c.redirect('/')

  const action = c.req.query('action')
  const page = Number(c.req.query('page') || '0')

  try {
    let result: SearchReturn | undefined
    let currentPage = page

    if (action === 'next' || action === 'back') {
      if (action === 'back' && page <= 0) {
        throw new HTTPException(400, {
          message: 'Cannot go back from first page',
        })
      }

      const targetPage = action === 'next' ? page + 1 : page - 1
      const cachedResult = pageCache.get(`${q}:${targetPage}`)

      if (cachedResult) {
        result = cachedResult
        currentPage = targetPage
      } else {
        const currentResult = pageCache.get(`${q}:${page}`)

        if (!currentResult) {
          throw new HTTPException(400, {
            message: 'No cached result for current page. Please search again.',
          })
        }

        if (action === 'next') {
          const continuation = await currentResult.getContinuation()
          currentPage = targetPage
          pageCache.set(`${q}:${currentPage}`, continuation)

          result = continuation
        } else {
          throw new HTTPException(400, {
            message: 'Previous page not found in cache. Please search again.',
          })
        }
      }
    } else {
      result = await c.get('yt').search(q, { sort_by: 'relevance' })
      currentPage = 0

      for (const [key] of pageCache) {
        if (key.startsWith(`${q}:`)) {
          pageCache.delete(key)
        }
      }

      pageCache.set(`${q}:${currentPage}`, result)
    }

    if (!result) {
      throw new HTTPException(500, { message: 'Search failed' })
    }

    const filteredData = filterData(result)
    const hasNextPage = Boolean(result.has_continuation)

    return c.render(
      <SearchPage
        data={filteredData}
        q={q}
        cgeo={c.get('cgeo')}
        ipcgeo={c.get('cgeo')}
        redirect={`/search?q=${encodeURIComponent(q)}&page=${currentPage}`}
        currentPage={currentPage}
        hasNextPage={hasNextPage}
      />,
    )
  } catch (error) {
    if ((error as Error).message?.includes('cache')) {
      return c.redirect(`/search?q=${encodeURIComponent(q)}`)
    }

    throw new HTTPException(500, { message: 'Search failed' })
  }
})

export default router
