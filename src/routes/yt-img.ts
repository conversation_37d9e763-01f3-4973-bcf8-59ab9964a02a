import { proxy } from 'hono/proxy'

import { Hono } from 'hono'

const router = new Hono<AppEnv>()

router.get('/vi/:path/:size', (c) =>
  proxy(`https://img.youtube.com/vi/${c.req.param('path')}/${c.req.param('size')}`, c.req),
)

router.get('/ggpht/:type/:path', (c) =>
  proxy(`https://yt3.ggpht.com/${c.req.param('type')}/${c.req.param('path')}`, c.req),
)

router.get('/ggpht/:path', (c) => proxy(`https://yt3.ggpht.com/${c.req.param('path')}`, c.req))

export default router
