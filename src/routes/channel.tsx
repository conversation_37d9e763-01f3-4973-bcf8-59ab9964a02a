import { HTTPException } from 'hono/http-exception'

import { Hono } from 'hono'

import ChannelPage from '../../views/Channel'
import MainLayout from '../../views/MainLayout'
import { mapChannelResponse, mapChannelVideo } from '../mappers/channel.mapper'
import { cgeo, innertube } from '../middlewares'

const router = new Hono<AppEnv>()

router.get('/channel/:id', cgeo(), innertube(), MainLayout('S60Tube - Channel'), async (c) => {
  const id = c.req.param('id')
  if (!id) throw new HTTPException(400, { message: 'Missing channel ID' })

  try {
    const channel = await c.get('yt').getChannel(id)
    const channelData = mapChannelResponse(channel)

    const channelInfo = {
      title: channel.metadata.title || '',
      external_id: channel.metadata.external_id || '',
      url: channel.metadata.url || '',
    }

    const videos = channel.videos
      .map((video) => mapChannelVideo(video, channelInfo))
      .filter(Boolean)

    return c.render(<ChannelPage channelData={channelData} videos={videos} />)
  } catch {
    throw new HTTPException(500, { message: 'Failed to fetch channel data' })
  }
})

export default router
