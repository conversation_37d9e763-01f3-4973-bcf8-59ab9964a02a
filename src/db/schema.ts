import { relations, sql } from 'drizzle-orm'
import { integer, sqliteTable, text, uniqueIndex } from 'drizzle-orm/sqlite-core'

export const emailAccounts = sqliteTable(
  'email_accounts',
  {
    id: integer('id').primaryKey({ autoIncrement: true }),
    email: text('email').notNull(),
    password: text('password').notNull(),
    refreshToken: text('refresh_token'),
    clientId: text('client_id'),
    recoveryEmail: text('recovery_email'),
    recoveryPassword: text('recovery_password'),
    isActive: integer('is_active', { mode: 'boolean' }).notNull().default(true),
    lastUsed: integer('last_used', { mode: 'timestamp' }),
    createdAt: integer('created_at', { mode: 'timestamp' })
      .notNull()
      .default(sql`(unixepoch())`),
    updatedAt: integer('updated_at', { mode: 'timestamp' })
      .notNull()
      .default(sql`(unixepoch())`),
    deletedAt: integer('deleted_at', { mode: 'timestamp' }),
  },
  (table) => [uniqueIndex('email_accounts_email_key').on(table.email)],
)

export const rapidApiAccounts = sqliteTable(
  'rapid_api_accounts',
  {
    id: integer('id').primaryKey({ autoIncrement: true }),
    emailAccountId: integer('email_account_id').references(() => emailAccounts.id, {
      onDelete: 'set null',
      onUpdate: 'cascade',
    }),
    userName: text('user_name'),
    password: text('password').notNull(),
    apiKey: text('api_key').notNull(),
    api1Remain: integer('api_1_remain').default(300),
    api2Remain: integer('api_2_remain').default(300),
    api3Remain: integer('api_3_remain').default(300),
    api4Remain: integer('api_4_remain').default(0),
    isActive: integer('is_active', { mode: 'boolean' }).notNull().default(true),
    lastUsed: integer('last_used', { mode: 'timestamp' }),
    createdAt: integer('created_at', { mode: 'timestamp' })
      .notNull()
      .default(sql`(unixepoch())`),
    updatedAt: integer('updated_at', { mode: 'timestamp' })
      .notNull()
      .$onUpdate(() => sql`(unixepoch())`)
      .default(sql`(unixepoch())`),
    deletedAt: integer('deleted_at', { mode: 'timestamp' }),
  },
  (table) => [
    uniqueIndex('rapid_api_accounts_email_account_id_key').on(table.emailAccountId),
    uniqueIndex('rapid_api_accounts_api_key_key').on(table.apiKey),
  ],
)

export const playableLinks = sqliteTable(
  'playable_links',
  {
    id: integer('id').primaryKey({ autoIncrement: true }),
    youtubeId: text('youtube_id').notNull(),
    type: text('type', { enum: ['video', 'audio'] }).default('video'),
    playableLink: text('playable_link').notNull(),
    isActive: integer('is_active', { mode: 'boolean' }).notNull().default(true),
    createdAt: integer('created_at', { mode: 'timestamp' })
      .notNull()
      .default(sql`(unixepoch())`),
    updatedAt: integer('updated_at', { mode: 'timestamp' })
      .notNull()
      .$onUpdate(() => sql`(unixepoch())`)
      .default(sql`(unixepoch())`),
    deletedAt: integer('deleted_at', { mode: 'timestamp' }),
  },
  (table) => [uniqueIndex('playable_links_youtube_id_type_key').on(table.youtubeId, table.type)],
)

export const twoxyaAccounts = sqliteTable('twoxya_accounts', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  username: text('username').notNull(),
  email: text('email'),
  password: text('password').notNull(),
  cookies: text('cookies'),
  createdAt: integer('created_at', { mode: 'timestamp' })
    .notNull()
    .default(sql`(unixepoch())`),
  updatedAt: integer('updated_at', { mode: 'timestamp' })
    .notNull()
    .$onUpdate(() => sql`(unixepoch())`)
    .default(sql`(unixepoch())`),
  deletedAt: integer('deleted_at', { mode: 'timestamp' }),
})

export const emailAccountsRelations = relations(emailAccounts, ({ one }) => ({
  rapidApiAccount: one(rapidApiAccounts, {
    fields: [emailAccounts.id],
    references: [rapidApiAccounts.emailAccountId],
  }),
}))

export const rapidApiAccountsRelations = relations(rapidApiAccounts, ({ one }) => ({
  emailAccount: one(emailAccounts, {
    fields: [rapidApiAccounts.emailAccountId],
    references: [emailAccounts.id],
  }),
}))

export type EmailAccount = typeof emailAccounts.$inferSelect
export type NewEmailAccount = typeof emailAccounts.$inferInsert

export type RapidApiAccount = typeof rapidApiAccounts.$inferSelect
export type NewRapidApiAccount = typeof rapidApiAccounts.$inferInsert

export type PlayableLink = typeof playableLinks.$inferSelect
export type NewPlayableLink = typeof playableLinks.$inferInsert

export type TwoxyaAccount = typeof twoxyaAccounts.$inferSelect
export type NewTwoxyaAccount = typeof twoxyaAccounts.$inferInsert
