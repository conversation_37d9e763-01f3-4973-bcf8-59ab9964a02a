import { HTTPException } from 'hono/http-exception'

import { PlayableLinkService } from '../services/playable-link'

import type { Context } from 'hono'

export async function videoPlaybackHandler(c: Context<AppEnv>) {
  const videoId = c.req.query('v')
  const drizzle = c.get('drizzle')
  if (!videoId) {
    throw new HTTPException(400, { message: 'Missing video ID' })
  }

  const { playableLink } = await PlayableLinkService.getPlayableLink(drizzle, videoId, 'video')

  if (!playableLink) {
    throw new HTTPException(404, { message: 'Video not found' })
  }

  return fetch(playableLink, c.req)
}
