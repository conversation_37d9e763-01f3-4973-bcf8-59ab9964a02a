import { HTTPException } from 'hono/http-exception'
import { proxy } from 'hono/proxy'

import Innertube from 'youtubei.js/cf-worker'

import { AccountService } from '../services/account'
import { ApiService } from '../services/api'
import { PlayableLinkService } from '../services/playable-link'
import { CONSTANTS, fetchWithRetry } from '../utils'

import type { VideoResponse } from '../types'
import type { Context } from 'hono'

export async function shortsStreamHandler(c: Context, streamId: string) {
  const reqId = c.req.query('reqId')

  if (!reqId) {
    throw new HTTPException(400, { message: 'Missing request ID' })
  }

  const videoId = await c.env.YT_SHORTS.get(`${reqId}:${streamId}`)

  if (!videoId) {
    throw new HTTPException(400, { message: 'Missing video ID' })
  }

  const nextStreamId = streamId === 'stream1' ? 'stream2' : 'stream1'

  c.executionCtx.waitUntil(
    (async () => {
      try {
        const yt = await Innertube.create({
          retrieve_player: false,
          location: String(c.req.raw.cf?.country),
        })

        const info = await yt.getShortsVideoInfo(videoId)

        const randomIndex = Math.floor(Math.random() * 10) + 1
        const nextVideoId = info.watch_next_feed?.[randomIndex]?.payload.videoId

        if (nextVideoId) {
          await c.env.YT_SHORTS.put(`${reqId}:${nextStreamId}`, nextVideoId, {
            expirationTtl: 60 * 60 * 24,
          })

          const { data: nextVideoData } = await fetchWithRetry<VideoResponse>({
            drizzle: c.get('drizzle'),
            fetchFn: async (apiKey, useApi1) => {
              const result = await ApiService.fetchVideoData(
                nextVideoId,
                apiKey,
                useApi1,
                c.get('cgeo'),
              )
              return result
            },
            updateRemainingFn: async (db, accId, isApi1, remain) => {
              await AccountService.updateApiRemaining(
                db,
                accId,
                isApi1 ? { api1Remain: remain } : { api2Remain: remain },
              )
            },
            getAccountFn: async (db) => {
              const acc = await AccountService.getAvailableAccount(db)
              return {
                id: acc.id,
                apiKey: acc.apiKey,
                api1Remain: acc.api1Remain,
              }
            },
          })

          const nextFormat = nextVideoData.data.formats.find((f) => f.itag === CONSTANTS.VIDEO_ITAG)
          if (nextFormat?.url) {
            PlayableLinkService.upsertPlayableLink(
              c.get('drizzle'),
              nextVideoId,
              nextFormat.url,
              'video',
            )
          }
        }
      } catch {}
    })(),
  )

  let playableLink = ''
  try {
    const { link, updatedAt } = await PlayableLinkService.getPlayableLinkWithUpdatedAt(
      c.get('drizzle'),
      videoId,
      'video',
    )
    const { playableLink: dbPlayableLink } = link

    const timestamp = new Date(updatedAt)
    const validHoursAgo = new Date(Date.now() - 1000 * 60 * 60 * 2)

    const isRecent = timestamp > validHoursAgo
    if (isRecent) {
      playableLink = dbPlayableLink
    }
  } catch {}

  if (!playableLink) {
    const { data: videoData, accountId: currentAccountId } = await fetchWithRetry<VideoResponse>({
      drizzle: c.get('drizzle'),
      fetchFn: async (apiKey, useApi1) => {
        const result = await ApiService.fetchVideoData(videoId, apiKey, useApi1, c.get('cgeo'))
        return result
      },
      updateRemainingFn: async (db, accId, isApi1, remain) => {
        await AccountService.updateApiRemaining(
          db,
          accId,
          isApi1 ? { api1Remain: remain } : { api2Remain: remain },
        )
      },
      getAccountFn: async (db) => {
        const acc = await AccountService.getAvailableAccount(db)
        return {
          id: acc.id,
          apiKey: acc.apiKey,
          api1Remain: acc.api1Remain,
        }
      },
    })

    c.executionCtx.waitUntil(AccountService.updateLastUsed(c.get('drizzle'), currentAccountId))

    const format = videoData.data.formats.find((f) => f.itag === CONSTANTS.VIDEO_ITAG)

    if (!format?.url) {
      throw new HTTPException(404, { message: 'Video format not found' })
    }

    playableLink = format.url

    c.executionCtx.waitUntil(
      PlayableLinkService.upsertPlayableLink(c.get('drizzle'), videoId, playableLink, 'video'),
    )
  }

  return proxy(playableLink, c.req)
}
