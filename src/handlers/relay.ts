import { HTTPException } from 'hono/http-exception'
import { proxy } from 'hono/proxy'

import { eq } from 'drizzle-orm'
import { fetcher } from 'itty-fetcher'

import * as schema from '../db/schema'
import {
  decodeHtmlEntities,
  getIndexByTime,
  headers,
  replaceRelativeHref,
  videoConverterUrl,
} from '../utils'

import type { Context } from 'hono'

export async function relayHandler(c: Context<AppEnv>) {
  if (c.req.query('mode') === 'handvideo' || c.req.query('mode') === 'makehandvideo') {
    const accountIndex = getIndexByTime()

    if (c.req.query('mode') === 'handvideo') {
      await c.env._2YXA.put(`id:${c.req.query('id')}`, String(accountIndex), {
        expirationTtl: 60 * 60 * 1, // 1 hour
      })
    }

    const data = await c
      .get('drizzle')
      .select({
        cookies: schema.twoxyaAccounts.cookies,
      })
      .from(schema.twoxyaAccounts)
      .where(eq(schema.twoxyaAccounts.id, Number(accountIndex)))
      .get()

    const html = await fetcher({ parse: 'text' }).get<string>(`${videoConverterUrl}/import.php`, {
      headers: headers(data?.cookies ?? ''),
      query: c.req.queries(),
    })

    if (c.req.query('mode') === 'makehandvideo') {
      const hrefMatch = html.match(/<a\b[^>]*href=["']([^"']+)["'][^>]*class=["']vse["'][^>]*>/is)
      const href = hrefMatch ? decodeHtmlEntities(hrefMatch[1]) : ''
      const url = new URL(`${videoConverterUrl}${href}`)

      const ready = url.searchParams.get('ready')

      if (ready && accountIndex) {
        await c.env._2YXA.put(`id:${ready}`, String(accountIndex), {
          expirationTtl: 60 * 60 * 1, // 1 hour
        })
      }
    }

    return c.html(replaceRelativeHref(html, `/relay?u=${encodeURIComponent(videoConverterUrl)}`))
  }

  const url = c.req.query('u')
  if (!url) throw new HTTPException(400, { message: 'Missing ?u= or ?mode=' })

  if (url.includes('2yxa.mobi')) {
    const decodedUrl = decodeHtmlEntities(decodeURIComponent(url))

    const blockedPaths = [
      'auth.php',
      'out.php',
      'cab.php',
      'archive.php',
      'speak.php',
      'section=anime',
    ]
    if (blockedPaths.some((p) => decodedUrl.includes(p))) {
      return c.text('Forbidden.', 403)
    }

    const isStatic = !decodedUrl.includes('.php') && !decodedUrl.includes('flash')
    const isForbidden =
      !decodedUrl.includes('import.php') &&
      !decodedUrl.includes('.mp') &&
      !decodedUrl.includes('.3gp') &&
      !decodedUrl.includes('.avi') &&
      !decodedUrl.includes('.flv') &&
      !decodedUrl.includes('.webm')

    if (isStatic) {
      return proxy(decodedUrl, c.req)
    }

    if (isForbidden) {
      return c.text('Forbidden.', 403)
    }

    let accountIndex: string | number | null = getIndexByTime()

    try {
      const uri = new URL(decodedUrl)
      const ready = uri.searchParams.get('ready')

      const accIndexByReady = await c.env._2YXA.get<string>(`id:${ready}`)

      accountIndex = accIndexByReady ? Number(accIndexByReady) : Number(accountIndex)
    } catch {}

    const data = await c
      .get('drizzle')
      .select({
        cookies: schema.twoxyaAccounts.cookies,
      })
      .from(schema.twoxyaAccounts)
      .where(eq(schema.twoxyaAccounts.id, Number(accountIndex)))
      .get()

    const html = await fetcher({ parse: 'text' }).get<string>(decodedUrl, {
      headers: headers(data?.cookies ?? ''),
    })
    return c.html(
      replaceRelativeHref(html, `/relay?u=${encodeURIComponent(videoConverterUrl)}`, {
        smFioLeft15Text: `${accountIndex}`,
      }),
    )
  }

  return proxy(decodeURIComponent(url), c.req)
}
