import { abbreviateTimeUnits, truncateText } from '../src/utils'
import Logo from './components/Logo'

import type { FC } from 'hono/jsx'

const styles = {
  searchInput: {
    flex: 4,
    padding: 4,
    fontSize: 'small',
    border: '1px solid #ccc',
  },
  searchButton: {
    flex: 1,
    padding: 4,
    fontSize: 'small',
    height: '100%',
    border: '1px solid #ccc',
  },
  videoItem: {
    paddingTop: '8px',
    paddingBottom: '8px',
    borderBottom: '1px solid #ddd',
    display: 'flex',
    fontSize: 'small',
  },
  videoMetaContainer: {
    color: '#666',
    fontSize: '11px',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: '2px',
  },
  videoTypeTag: {
    fontWeight: 400,
    border: '1px solid #ccc',
    padding: '1px 4px',
  },
  durationTag: {
    color: '#fff',
    fontWeight: 500,
    backgroundColor: '#212529',
    border: '1px solid #212529',
    padding: '1px 4px',
  },
  titleLink: {
    textDecoration: 'none',
    display: 'block',
    paddingBottom: 2,
  },
  metaText: {
    fontSize: 'small',
    color: '#666',
    paddingBottom: 2,
  },
  mainContainer: {
    fontSize: 'small',
  },
  thumbnailContainer: {
    position: 'relative',
  },
  videoInfoContainer: {
    marginLeft: '8px',
    flex: 1,
  },
  headerContainer: {
    color: '#333',
    borderBottom: '1px solid #ebebeb',
    paddingBottom: '6px',
    display: 'flex',
    alignItems: 'center',
    margin: '6px 2px',
  },
  channelHeader: {
    padding: '12px 2px',
    borderBottom: '1px solid #ebebeb',
    marginBottom: '8px',
  },
  channelTitle: {
    fontSize: '18px',
    fontWeight: 'bold',
    marginBottom: '4px',
  },
  channelMeta: {
    fontSize: 'small',
    color: '#666',
    marginBottom: '8px',
  },
  channelDescription: {
    fontSize: 'small',
    color: '#333',
    lineHeight: '1.4',
  },
  videoButton: {
    display: 'inline-block',
    padding: '2px 4px',
    background: '#cc0000',
    color: 'white',
    textDecoration: 'none',
    fontSize: '12px',
    marginBottom: '4px',
    marginRight: '4px',
    border: '1px solid #aa0000',
  },
  audioButton: {
    display: 'inline-block',
    padding: '2px 4px',
    background: '#065fd4',
    color: 'white',
    textDecoration: 'none',
    fontSize: '12px',
    border: '1px solid #044baa',
  },
}

const LazyImage: FC = ({ src, width, height }) => {
  return (
    <img
      src={src}
      width={width}
      height={height}
      loading="lazy"
      decoding="async"
      style={{ backgroundColor: '#f1f1f1' }}
    />
  )
}

const Video: FC = ({ item }) => {
  const {
    videoId: id,
    title,
    viewCountText: viewCount,
    publishedText,
    lengthSeconds: duration,
  } = item
  const publishedDate = abbreviateTimeUnits(publishedText)

  const formatDuration = (seconds: number) => {
    if (!seconds) return ''
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }

  return (
    <div style={styles.videoItem}>
      <div style={styles.thumbnailContainer}>
        <a href={`/video/${id}`} rel="prefetch">
          <LazyImage
            src={`https://img.youtube.com/vi/${id}/mqdefault.jpg`}
            width="120"
            height="68"
          />
        </a>
        <div style={styles.videoMetaContainer}>
          <span style={styles.videoTypeTag}>Video</span>
          <span style={styles.durationTag}>{formatDuration(duration)}</span>
        </div>
      </div>

      <div style={styles.videoInfoContainer}>
        <a href={`/video/${id}`} style={styles.titleLink}>
          {truncateText(title, 65)}
        </a>

        <div style={styles.metaText}>
          {viewCount} • {truncateText(publishedDate, 14)}
        </div>

        <div>
          <a href={`/video/${id}`} style={styles.videoButton}>
            Video
          </a>
          <a href={`/audio/${id}`} style={styles.audioButton}>
            Audio
          </a>
        </div>
      </div>
    </div>
  )
}

const ChannelPage: FC = ({ channelData, videos }) => {
  const videoComponents = videos.map((item: { videoId: string }) => (
    <Video item={item} key={item.videoId} />
  ))

  return (
    <div style={styles.mainContainer}>
      <div>
        {/* Logo */}
        <div
          style={{
            margin: '2px 2px 0 2px',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <Logo center={false} large={false} />
        </div>

        {/* Channel Header */}
        <div style={styles.channelHeader}>
          <div style={styles.channelTitle}>{channelData.author}</div>
          <div style={styles.channelMeta}>
            {channelData.subCount ? `${channelData.subCount.toLocaleString()} subscribers` : ''} •{' '}
            {videos.length} videos
          </div>
          {channelData.description && (
            <div style={styles.channelDescription}>
              {truncateText(channelData.description, 200)}
            </div>
          )}
        </div>

        {/* Videos Title */}
        <div style={styles.headerContainer}>
          Latest videos from <strong>{truncateText(channelData.author, 25)}</strong>
        </div>

        {/* Main Content - Videos */}
        <div>
          {videoComponents.length > 0 ? (
            videoComponents
          ) : (
            <div style={{ padding: '20px 0', textAlign: 'center', color: '#666' }}>
              No videos found for this channel.
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default ChannelPage
