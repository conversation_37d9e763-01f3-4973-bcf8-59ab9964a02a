import { jsx<PERSON>ender<PERSON> } from 'hono/jsx-renderer'

import { webBaseUrl } from '../src/utils'

export default (title: string) =>
  jsxRenderer(
    ({ children }) => {
      return (
        <html xmlns="http://www.w3.org/1999/xhtml" lang="en">
          <head>
            <meta charset="UTF-8" />
            <title>{title}</title>
            <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
            <meta
              name="viewport"
              content="width=device-width, initial-scale=1.0, maximum-scale=1.0"
            />
            <link rel="shortcut icon" href="/favicon.ico" />
            <link rel="canonical" href={webBaseUrl} />

            <meta
              name="description"
              content="S60Tube - Watch YouTube videos on your Nokia S60 device. The easiest way to access YouTube content on legacy Nokia phones."
            />
            <meta
              name="keywords"
              content="S60Tube, Nokia S60, YouTube mobile, Nokia YouTube, legacy phone YouTube, retro phone YouTube, S60 video player"
            />
            <meta property="og:title" content="S60Tube - YouTube for Nokia S60 Devices" />
            <meta property="og:type" content="website" />
            <meta property="og:url" content={webBaseUrl} />
            <meta property="og:image" content="/favicon.ico" />
            <meta
              property="og:description"
              content="Watch YouTube videos on your Nokia S60 device. The easiest way to access YouTube content on legacy Nokia phones."
            />
          </head>
          <body
            style={{
              fontFamily: 'Arial, sans-serif',
              maxWidth: '600px',
              margin: '0 auto',
            }}
          >
            {children}
          </body>
        </html>
      )
    },
    {
      docType:
        '<!DOCTYPE html PUBLIC "-//WAPFORUM//DTD XHTML Mobile 1.0//EN" "http://www.wapforum.org/DTD/xhtml-mobile10.dtd">',
    },
  )
