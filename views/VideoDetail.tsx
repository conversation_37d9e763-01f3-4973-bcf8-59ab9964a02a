import type { FC } from 'hono/jsx'

const StreamingInstructions: FC = () => {
  return (
    <>
      <h2 style={styles.h2}>Streaming</h2>
      <h3 style={styles.h3}>CorePlayer</h3>
      <h4 style={styles.h4}>
        Please copy the link below and use{' '}
        <a href="/static/coreplayer.sis" style={styles.link}>
          <span>CorePlayer</span>
        </a>{' '}
        to play it.
      </h4>

      <p style={styles.instructionText}>
        (Open <b>CorePlayer</b>
        {' > '}
        <u>Menu</u>
        {' > '}
        <u>Open URL...</u>
        {' > '}
        Paste link)
      </p>
    </>
  )
}

const DownloadSection: FC = (props) => {
  return (
    <div style={styles.downloadSection}>
      <h2 style={styles.h2}>Download</h2>
      <div style={styles.downloadInfo}>
        <span>
          <b>File information</b>
        </span>
      </div>

      <div style={styles.infoItem}>
        <span>
          <b>Quality:</b>{' '}
        </span>
        <span>
          {props.format.quality_label}
          {props.format.fps || ''}
        </span>
      </div>

      <div style={styles.infoItem}>
        <span>
          <b>MIME:</b>{' '}
        </span>
        <span>{props.format.mime_type}</span>
      </div>

      <a href={props.url}>
        <button>Download</button>
      </a>
    </div>
  )
}

const TwoYaXA: FC<{ convertUrl: string; importUrl?: string }> = ({ convertUrl, importUrl }) => {
  return (
    <div style={styles.twoYaXASection}>
      <h3 style={{ ...styles.h3, marginBottom: '16px' }}>Via 2yxa.mobi</h3>
      <div style={styles.infoItem}>
        <a href={convertUrl}>
          <button>rtsp mp4 (320x180) high bitrate</button>
        </a>
      </div>

      <div style={styles.infoItem}>
        <a href={`${convertUrl}?type=3gp`}>
          <button>rtsp 3gp (352x288) high bitrate</button>
        </a>
      </div>

      <div style={styles.infoItem}>
        <a href={`${convertUrl}?type=3gp-low`}>
          <button>rtsp 3gp (352x288) low bitrate</button>
        </a>
      </div>

      <div style={styles.infoItem}>
        <a href={`${convertUrl}?type=mp3`}>
          <button>mp3 (64kbps)</button>
        </a>
      </div>

      <div style={styles.infoItem}>
        <a href={importUrl} style={styles.link}>
          <button>Select quality</button>
        </a>
      </div>
    </div>
  )
}

const DetailPage: FC = (props) => {
  return (
    <main style={styles.container}>
      <div style={styles.content}>
        <StreamingInstructions />
        <input style={styles.input} value={props.url} />

        {props.url.includes('video') && (
          <TwoYaXA convertUrl={props.convertUrl} importUrl={props.importUrl} />
        )}

        <DownloadSection url={props.url} format={props.format} />
      </div>
    </main>
  )
}

const styles = {
  container: {
    width: '100%',
    display: 'flex',
    justifyContent: 'center',
    padding: '0 4px',
  },
  content: {
    width: '100%',
    maxWidth: '600px',
  },
  twoYaXASection: {
    margin: '24px 0px',
  },
  h2: {
    fontSize: '2rem',
    margin: '12px 0px',
  },
  h3: {
    fontSize: '1.5rem',
    margin: '4px 0px',
  },
  h4: {
    margin: '4px 0px',
    fontSize: '1rem',
  },
  link: {
    color: '#0000EE',
    textDecoration: 'none',
  },
  instructionText: {
    fontSize: '14px',
    margin: '8px 0px',
  },
  input: {
    width: '100%',
    marginTop: '8px',
    fontSize: '14px',
    boxSizing: 'border-box',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    cursor: 'pointer',
    padding: '8px',
    backgroundColor: '#f5f5f5',
    border: '1px solid #ddd',
  },
  downloadSection: {
    margin: '24px 0px',
    fontSize: '14px',
  },
  downloadInfo: {
    marginBottom: '8px',
    fontSize: '1rem',
  },
  infoItem: {
    marginBottom: '8px',
  },
}

export default DetailPage
