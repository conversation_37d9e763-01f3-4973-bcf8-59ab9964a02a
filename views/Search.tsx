import { abbreviateTimeUnits, truncateText } from '../src/utils'
import Logo from './components/Logo'

import type { FC } from 'hono/jsx'

function get<T = unknown>(obj: unknown, path: string | string[], defaultValue?: T): T {
  const keys = Array.isArray(path) ? path : path.split('.')
  let result: unknown = obj

  for (const key of keys) {
    if (result == null) return defaultValue as T

    if (key.startsWith('[') && key.endsWith(']')) {
      const index = parseInt(key.slice(1, -1), 10)
      result = (result as unknown[])[index]
    } else {
      result = (result as Record<string, unknown>)[key]
    }
  }

  return result === undefined ? (defaultValue as T) : (result as T)
}

const styles = {
  searchInput: {
    flex: 4,
    padding: 4,
    fontSize: 'small',
    border: '1px solid #ccc',
  },
  searchButton: {
    flex: 1,
    padding: 4,
    fontSize: 'small',
    height: '100%',
    border: '1px solid #ccc',
  },
  videoItem: {
    paddingTop: '8px',
    paddingBottom: '8px',
    borderBottom: '1px solid #ddd',
    display: 'flex',
    fontSize: 'small',
  },
  videoMetaContainer: {
    color: '#666',
    fontSize: '11px',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: '2px',
  },
  videoTypeTag: {
    fontWeight: 400,
    border: '1px solid #ccc',
    padding: '1px 4px',
  },
  durationTag: {
    color: '#fff',
    fontWeight: 500,
    backgroundColor: '#212529',
    border: '1px solid #212529',
    padding: '1px 4px',
  },
  titleLink: {
    textDecoration: 'none',
    display: 'block',
    paddingBottom: 2,
  },
  metaText: {
    fontSize: 'small',
    color: '#666',
    paddingBottom: 2,
  },
  mainContainer: {
    fontSize: 'small',
  },
  thumbnailContainer: {
    position: 'relative',
  },
  videoInfoContainer: {
    marginLeft: '8px',
    flex: 1,
  },
  headerContainer: {
    color: '#333',
    borderBottom: '1px solid #ebebeb',
    paddingBottom: '6px',
    display: 'flex',
    alignItems: 'center',
    margin: '6px 2px',
  },
  resultCount: {
    marginLeft: '8px',
    color: '#606060',
  },
  paginationContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    gap: '12px',
    padding: '16px 0',
  },
  pageInfo: {
    fontSize: 'small',
    color: '#666',
    margin: '0 8px',
  },
  videoButton: {
    display: 'inline-block',
    padding: '2px 4px',
    background: '#cc0000',
    color: 'white',
    textDecoration: 'none',
    fontSize: '12px',
    marginBottom: '4px',
    marginRight: '4px',
    border: '1px solid #aa0000',
  },
  audioButton: {
    display: 'inline-block',
    padding: '2px 4px',
    background: '#065fd4',
    color: 'white',
    textDecoration: 'none',
    fontSize: '12px',
    border: '1px solid #044baa',
  },
  channelLink: {
    fontSize: 'small',
    textDecoration: 'none',
    color: '#8c113cff',
  },
}

const SearchBar: FC = ({ q }) => {
  return (
    <form action="/search" method="get">
      <div style={{ display: 'flex' }}>
        <input
          id="inputField"
          name="q"
          type="text"
          maxLength={128}
          style={styles.searchInput}
          value={q}
          placeholder="Search Youtube"
        />
        <input type="submit" value="Search" style={styles.searchButton} />
      </div>
    </form>
  )
}

const LazyImage: FC = ({ src, width, height }) => {
  return (
    <img
      src={src}
      width={width}
      height={height}
      loading="lazy"
      decoding="async"
      style={{ backgroundColor: '#f1f1f1' }}
    />
  )
}

const Video: FC = ({ item }) => {
  const id = get<string>(item, 'id', '')
  const type = get<unknown[]>(item, 'thumbnail_overlays', [])
  const isReel = get<string>(type, '[0].text', '').toUpperCase() === 'SHORTS'
  const title = get<string>(item, 'title.text', '')
  const viewCount = get<string>(item, 'short_view_count.text', '')
  const publishedDate = abbreviateTimeUnits(get<string>(item, 'published.text', ''))
  const authorName = get<string>(item, 'author.name', '')
  const authorId = get<string>(item, 'author.id', '')
  const duration = get<string>(item, 'duration.text', '')

  return (
    <div style={styles.videoItem}>
      <div style={styles.thumbnailContainer}>
        <a href={`/video/${id}`} rel="prefetch">
          <LazyImage
            src={`https://img.youtube.com/vi/${id}/mqdefault.jpg`}
            width="120"
            height="68"
          />
        </a>
        <div style={styles.videoMetaContainer}>
          <span style={styles.videoTypeTag}>{isReel ? 'Shorts' : 'Video'}</span>
          <span style={styles.durationTag}>{duration}</span>
        </div>
      </div>

      <div style={styles.videoInfoContainer}>
        <a href={`/video/${id}`} style={styles.titleLink}>
          {truncateText(title, 65)}
        </a>

        <div style={styles.metaText}>
          {viewCount} • {truncateText(publishedDate, 14)}
        </div>

        <div
          style={{
            paddingBottom: 4,
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
          }}
        >
          {authorId ? (
            <a href={`/channel/${authorId}`} style={styles.channelLink}>
              {truncateText(authorName, 25)}
            </a>
          ) : (
            truncateText(authorName, 25)
          )}
        </div>

        <div>
          <a href={`/video/${id}`} style={styles.videoButton}>
            Video
          </a>
          <a href={`/audio/${id}`} style={styles.audioButton}>
            Audio
          </a>
        </div>
      </div>
    </div>
  )
}

const NavigationButtons: FC = ({ q, currentPage = 0, hasNextPage = true }) => {
  const canGoBack = currentPage > 0

  return (
    <div style={styles.paginationContainer}>
      {canGoBack ? (
        <a href={`/search?q=${encodeURIComponent(q)}&action=back&page=${currentPage}`}>
          <button>Back</button>
        </a>
      ) : (
        <span>Back</span>
      )}

      <span style={styles.pageInfo}>Page {currentPage + 1}</span>

      {hasNextPage ? (
        <a href={`/search?q=${encodeURIComponent(q)}&action=next&page=${currentPage}`}>
          <button>Next</button>
        </a>
      ) : (
        <span>Next</span>
      )}
    </div>
  )
}

const SearchPage: FC = ({
  q,
  data,
  // cgeo,
  // ipcgeo,
  // redirect,
  currentPage = 0,
  hasNextPage = true,
}) => {
  const videos = data.map((item: { id: string }) => <Video item={item} key={item.id} />)

  return (
    <div style={styles.mainContainer}>
      <div>
        {/* Logo and Region */}
        <div
          style={{
            margin: '2px 2px 0 2px',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <Logo center={false} large={false} />

          {/* <div>
            Region:{' '}
            {cgeo === 'VN' ? (
              <span>VN</span>
            ) : (
              <a
                href={`/set-cookie?cgeo=VN&redirect=${redirect}`}
                style={{ color: 'blue', textDecoration: 'underline' }}
              >
                VN
              </a>
            )}{' '}
            /{' '}
            {cgeo === ipcgeo ? (
              <span>{ipcgeo}</span>
            ) : (
              <a
                href={`/set-cookie?cgeo=${ipcgeo}&redirect=${redirect}`}
                style={{ color: 'blue', textDecoration: 'underline' }}
              >
                {ipcgeo}
              </a>
            )}
          </div> */}
        </div>

        {/* Search Bar */}
        <div style={{ margin: '2px' }}>
          <SearchBar q={q} />
        </div>

        {/* Search Results Title */}
        {q && (
          <div style={styles.headerContainer}>
            Search results for "<strong>{truncateText(q, 25)}</strong>"
          </div>
        )}

        {/* Main Content - Videos */}
        <div>
          {videos.length > 0 ? (
            videos
          ) : (
            <div style={{ padding: '20px 0', textAlign: 'center', color: '#666' }}>
              No videos found. Try a different search term.
            </div>
          )}
        </div>

        {/* Navigation Buttons */}
        {videos.length > 0 && (
          <NavigationButtons q={q} currentPage={currentPage} hasNextPage={hasNextPage} />
        )}
      </div>
    </div>
  )
}

export default SearchPage
