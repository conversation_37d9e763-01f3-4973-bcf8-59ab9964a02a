{"compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "strict": true, "skipLibCheck": true, "lib": ["ESNext", "DOM"], "jsx": "react-jsx", "jsxImportSource": "hono/jsx", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true}, "include": ["**/*.ts", "**/*.tsx", "*.d.ts"]}