{"version": "6", "dialect": "sqlite", "id": "c4e879b0-3b79-4211-bccf-1ee5543c622c", "prevId": "********-0000-0000-0000-************", "tables": {"email_accounts": {"name": "email_accounts", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "client_id": {"name": "client_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "recovery_email": {"name": "recovery_email", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "recovery_password": {"name": "recovery_password", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "last_used": {"name": "last_used", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}, "deleted_at": {"name": "deleted_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"email_accounts_email_key": {"name": "email_accounts_email_key", "columns": ["email"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "playable_links": {"name": "playable_links", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "youtube_id": {"name": "youtube_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'video'"}, "playable_link": {"name": "playable_link", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "last_used": {"name": "last_used", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}, "deleted_at": {"name": "deleted_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"playable_links_youtube_id_type_key": {"name": "playable_links_youtube_id_type_key", "columns": ["youtube_id", "type"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "rapid_api_accounts": {"name": "rapid_api_accounts", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "email_account_id": {"name": "email_account_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "user_name": {"name": "user_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "api_key": {"name": "api_key", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "api_1_remain": {"name": "api_1_remain", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 300}, "api_2_remain": {"name": "api_2_remain", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 300}, "api_3_remain": {"name": "api_3_remain", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 300}, "api_4_remain": {"name": "api_4_remain", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "last_used": {"name": "last_used", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}, "deleted_at": {"name": "deleted_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"rapid_api_accounts_email_account_id_key": {"name": "rapid_api_accounts_email_account_id_key", "columns": ["email_account_id"], "isUnique": true}, "rapid_api_accounts_api_key_key": {"name": "rapid_api_accounts_api_key_key", "columns": ["api_key"], "isUnique": true}}, "foreignKeys": {"rapid_api_accounts_email_account_id_email_accounts_id_fk": {"name": "rapid_api_accounts_email_account_id_email_accounts_id_fk", "tableFrom": "rapid_api_accounts", "tableTo": "email_accounts", "columnsFrom": ["email_account_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}