CREATE TABLE `email_accounts` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`email` text NOT NULL,
	`password` text NOT NULL,
	`refresh_token` text,
	`client_id` text,
	`recovery_email` text,
	`recovery_password` text,
	`is_active` integer DEFAULT true NOT NULL,
	`last_used` integer,
	`created_at` integer DEFAULT (unixepoch()) NOT NULL,
	`updated_at` integer DEFAULT (unixepoch()) NOT NULL,
	`deleted_at` integer
);
--> statement-breakpoint
CREATE UNIQUE INDEX `email_accounts_email_key` ON `email_accounts` (`email`);--> statement-breakpoint
CREATE TABLE `playable_links` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`youtube_id` text NOT NULL,
	`type` text DEFAULT 'video',
	`playable_link` text NOT NULL,
	`is_active` integer DEFAULT true NOT NULL,
	`last_used` integer,
	`created_at` integer DEFAULT (unixepoch()) NOT NULL,
	`updated_at` integer DEFAULT (unixepoch()) NOT NULL,
	`deleted_at` integer
);
--> statement-breakpoint
CREATE UNIQUE INDEX `playable_links_youtube_id_type_key` ON `playable_links` (`youtube_id`,`type`);--> statement-breakpoint
CREATE TABLE `rapid_api_accounts` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`email_account_id` integer,
	`user_name` text,
	`password` text NOT NULL,
	`api_key` text NOT NULL,
	`api_1_remain` integer DEFAULT 300,
	`api_2_remain` integer DEFAULT 300,
	`api_3_remain` integer DEFAULT 300,
	`api_4_remain` integer DEFAULT 0,
	`is_active` integer DEFAULT true NOT NULL,
	`last_used` integer,
	`created_at` integer DEFAULT (unixepoch()) NOT NULL,
	`updated_at` integer DEFAULT (unixepoch()) NOT NULL,
	`deleted_at` integer,
	FOREIGN KEY (`email_account_id`) REFERENCES `email_accounts`(`id`) ON UPDATE cascade ON DELETE set null
);
--> statement-breakpoint
CREATE UNIQUE INDEX `rapid_api_accounts_email_account_id_key` ON `rapid_api_accounts` (`email_account_id`);--> statement-breakpoint
CREATE UNIQUE INDEX `rapid_api_accounts_api_key_key` ON `rapid_api_accounts` (`api_key`);